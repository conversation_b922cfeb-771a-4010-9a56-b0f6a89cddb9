{"permissions": {"allow": ["Bash(grep:*)", "Bash(rg:*)", "Bash(find:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "Bash(ls:*)", "Bash(pnpm build:*)", "Bash(timeout 30 pnpm dev)", "Bash(npm run build:*)", "Bash(npx tsc:*)", "Bash(npm run type-check:*)", "Bash(npm run:*)", "Bash(git add:*)", "Bash(git config:*)", "Bash(pnpm lint:*)", "Bash(python -m pytest tests/test_evolution_status.py -v)", "Bash(python3 -m pytest tests/test_evolution_status.py -v)", "Bash(python -m pytest -xvs -k \"test_config\" --tb=short)", "<PERSON><PERSON>(python3:*)", "Bash(node:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(docker:*)", "Bash(timeout 90 docker-compose logs -f --tail=0 openmemory-mcp)", "Bash(npm install:*)", "<PERSON><PERSON>(playwright install:*)", "Bash(node test-pages.js)", "Bash(DISPLAY=:0 xvfb-run -a node test-pages.js)", "Bash(node:*)", "<PERSON><PERSON>(timeout:*)", "WebFetch(domain:localhost)", "Bash(git commit:*)", "Bash(pnpm add:*)", "<PERSON><PERSON>(npx playwright:*)", "<PERSON><PERSON>(sudo npx playwright:*)", "Bash(echo)", "Bash(ss:*)"], "deny": []}}