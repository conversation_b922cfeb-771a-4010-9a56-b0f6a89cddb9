# Memory Master v2 - User Manual

This comprehensive user guide will walk you through all aspects of using Memory Master v2, from initial setup to advanced memory management operations.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Account Management](#account-management)
3. [MCP Integration](#mcp-integration)
4. [Web Dashboard Usage](#web-dashboard-usage)
5. [Memory Operations](#memory-operations)
6. [API Usage](#api-usage)
7. [Evolution Intelligence](#evolution-intelligence)
8. [Best Practices](#best-practices)
9. [Troubleshooting](#troubleshooting)

## Getting Started

### First-Time Setup

1. **Verify Installation**: Ensure Memory Master v2 is running by visiting:
   - Web Dashboard: http://localhost:3000
   - API Documentation: http://localhost:8765/docs

2. **Check System Health**: Navigate to the dashboard and verify all services are green.

3. **Create Your First Memory**: Use the web interface or MCP tools to add a test memory.

### Understanding the System

Memory Master v2 consists of three main components:
- **Memory Storage**: Your personal memories stored with vector embeddings
- **Applications**: Different AI tools that can access your memories (Claude, VS Code, etc.)
- **Evolution Intelligence**: Automatic memory optimization system

## Account Management

### User Accounts

Memory Master v2 uses a simple user identification system:

1. **User ID**: Set in your environment variables (`USER` in api/.env)
2. **Default User**: Automatically created on first startup
3. **No Passwords**: Authentication is handled through environment configuration

### Managing Applications

Each AI tool that connects to your memory system is treated as an "application":

**Creating Applications**:
1. Navigate to the "Apps" section in the web dashboard
2. Click "Add New App"
3. Provide a descriptive name (e.g., "Claude Desktop", "VS Code")
4. Configure access permissions

**Application States**:
- **Active**: App can read and write memories
- **Paused**: App can only read existing memories
- **Archived**: App has no access to memories

## MCP Integration

### Claude Desktop Integration

**Step 1: Configure Claude Desktop**

Add this configuration to your Claude Desktop settings:

```json
{
  "mcpServers": {
    "memory-master": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "env": {
        "MEMORY_API_URL": "http://localhost:8765",
        "USER_ID": "tradelink"
      }
    }
  }
}
```

**Step 2: Restart Claude Desktop**

Close and reopen Claude Desktop to load the new MCP server.

**Step 3: Verify Connection**

In a new Claude conversation, try:
```
Please search my memories for "test"
```

### VS Code Integration

**Step 1: Install MCP Extension**

Install the Model Context Protocol extension for VS Code.

**Step 2: Configure Settings**

Add to your VS Code settings.json:
```json
{
  "mcp.servers": {
    "memory-master": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "env": {
        "MEMORY_API_URL": "http://localhost:8765"
      }
    }
  }
}
```

### Other MCP-Compatible Tools

Memory Master v2 works with any MCP-compatible application:
- **Cursor**: Follow VS Code instructions
- **Windsurf**: Use similar MCP configuration
- **Custom Tools**: Implement MCP client using the standard protocol

## Web Dashboard Usage

### Dashboard Overview

The web dashboard provides a comprehensive interface for managing your memory system:

**Main Sections**:
1. **Dashboard**: System overview and health status
2. **Memories**: Browse, search, and manage your memories
3. **Apps**: Manage connected applications
4. **Analytics**: View memory usage statistics
5. **Settings**: Configure system behavior

### Memory Management

**Viewing Memories**:
1. Navigate to the "Memories" section
2. Use filters to find specific memories:
   - By application
   - By date range
   - By content type
   - By state (active, paused, archived)

**Editing Memories**:
1. Click on any memory to view details
2. Edit content, metadata, or state
3. Save changes to update the memory

**Deleting Memories**:
1. Select memories to delete
2. Choose between:
   - **Soft Delete**: Mark as deleted (can be recovered)
   - **Hard Delete**: Permanently remove from system

### Application Management

**Monitoring App Usage**:
1. View memory access statistics per app
2. See recent activity and access patterns
3. Monitor memory creation and retrieval rates

**Configuring App Permissions**:
1. Set read/write permissions per app
2. Configure memory retention policies
3. Enable/disable evolution intelligence per app

## Memory Operations

### Adding Memories

**Via Web Dashboard**:
1. Click "Add Memory" button
2. Enter memory content
3. Add relevant metadata (tags, categories)
4. Select target application
5. Save the memory

**Via MCP Tools**:
```
Please remember: "I prefer using TypeScript for all new projects because it catches errors early and improves code maintainability."
```

**Via API**:
```bash
curl -X POST "http://localhost:8765/api/v1/memories" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Important project decision",
    "metadata_": {"project": "memory-master", "type": "decision"},
    "user_id": "tradelink",
    "app_id": "your-app-id"
  }'
```

### Searching Memories

**Semantic Search**:
Memory Master v2 uses vector embeddings for intelligent search:

```
Find memories about "database optimization"
```

This will return memories related to database performance, even if they don't contain the exact phrase.

**Filtered Search**:
- Search within specific applications
- Filter by date ranges
- Search by memory state
- Filter by metadata tags

### Memory States

**Active**: Memory is available for retrieval and can be updated
**Paused**: Memory is read-only, cannot be modified
**Archived**: Memory is stored but not actively searched
**Deleted**: Memory is marked for deletion but not yet removed

## API Usage

### Authentication

API requests require either:
1. **Environment-based**: User ID from environment variables
2. **Header-based**: `X-User-ID` header in requests
3. **API Key**: Optional API key for additional security

### Core Endpoints

**Add Memory**:
```bash
POST /api/v1/memories
{
  "content": "Memory content",
  "metadata_": {"key": "value"},
  "user_id": "uuid",
  "app_id": "uuid"
}
```

**Search Memories**:
```bash
GET /api/v1/memories/search?query=search_term&limit=10
```

**List Memories**:
```bash
GET /api/v1/memories?app_id=uuid&limit=50&offset=0
```

**Update Memory**:
```bash
PUT /api/v1/memories/{memory_id}
{
  "content": "Updated content",
  "state": "active"
}
```

**Delete Memory**:
```bash
DELETE /api/v1/memories/{memory_id}?hard_delete=false
```

### MCP Tool Endpoints

**Available Tools**:
- `add_memories`: Store new memories
- `search_memory`: Semantic search
- `list_memories`: Browse memories
- `get_system_health`: System status
- `get_evolution_metrics`: Intelligence metrics

## Evolution Intelligence

### Overview

Evolution Intelligence is the advanced AI-powered system that makes Memory Master v2 truly intelligent. It consists of two main features:

1. **Enable Evolution**: Controls whether memory evolution processing is active
2. **Auto-Optimization**: Automatically optimizes memory structure and performance

### Understanding Evolution Operations

Evolution Intelligence automatically optimizes your memory system through four types of operations:

- **ADD**: Creating new memories from conversations and interactions
- **UPDATE**: Modifying existing memories with new information or corrections
- **DELETE**: Removing outdated, redundant, or incorrect memories
- **NOOP**: No operation needed (memory is already optimal)

### The Two Core Features

#### Enable Evolution
**What it does**: Controls the entire evolution processing system
**When enabled**:
- Memories are automatically processed and optimized
- New memories are intelligently extracted from conversations
- Existing memories are updated with new information
- Redundant or outdated memories are cleaned up
- Processing queue is active and operations are performed

**When disabled**:
- No automatic memory processing occurs
- Memories remain static until manually modified
- Processing queue is empty
- System operates in "manual mode" only

#### Auto-Optimization
**What it does**: Automatically optimizes memory structure and performance
**When enabled** (requires Evolution to be ON):
- Memory organization is continuously improved
- Performance bottlenecks are automatically resolved
- Memory quality scores are enhanced
- Conflict resolution is automated
- System learns and adapts to usage patterns

**When disabled**:
- Manual optimization only
- No automatic performance improvements
- Memory structure remains as-is until manually optimized

### Frequently Asked Question: When Should I Turn These OFF?

**Question**: "Since these are advanced features that make the memory system intelligent, shouldn't they always be ON? Why would I ever need to turn them off?"

**Answer**: You're absolutely right that for most users, these should stay ON. However, there are specific scenarios where turning them off is valuable:

#### When to Turn OFF Enable Evolution

**1. Development & Debugging** 🔧
```
Scenario: You're developing new memory features or debugging issues
Problem: Evolution keeps modifying memories while you're testing
Solution: Turn OFF evolution to get predictable, stable memory states
Example: Testing a new MCP tool and need consistent memory data
```

**2. Data Migration & Bulk Operations** 📦
```
Scenario: Importing large datasets or migrating from another system
Problem: Evolution processing creates overhead and conflicts during bulk operations
Solution: Disable evolution during migration, re-enable after completion
Example: Importing 10,000 memories from another system
```

**3. Performance Troubleshooting** ⚡
```
Scenario: System is running slowly, need to isolate the cause
Problem: Is it the evolution processing or something else?
Solution: Temporarily disable evolution to measure baseline performance
Example: Investigating why memory searches are taking too long
```

**4. Compliance & Audit Requirements** 📋
```
Scenario: Legal/compliance review requires exact memory preservation
Problem: Evolution might modify memories during audit period
Solution: Freeze evolution to maintain exact historical state
Example: Legal discovery process requiring unchanged memory records
```

**5. Resource Conservation** 💰
```
Scenario: High-traffic periods or limited compute resources
Problem: Evolution processing uses CPU/memory resources
Solution: Temporarily disable to allocate resources to core operations
Example: During peak usage hours, prioritize memory retrieval over processing
```

#### When to Turn OFF Auto-Optimization

**1. Custom Optimization Strategies** 🎯
```
Scenario: You want manual control over optimization timing
Problem: Auto-optimization might conflict with planned maintenance
Solution: Disable auto, run manual optimization during off-peak hours
Example: Scheduled maintenance windows for optimization
```

**2. Testing Specific Configurations** 🧪
```
Scenario: Testing how system performs with current memory structure
Problem: Auto-optimization changes the structure during testing
Solution: Disable to maintain consistent test conditions
Example: Performance benchmarking with fixed memory organization
```

**3. Gradual Rollout** 🚀
```
Scenario: New deployment, want to monitor evolution first
Problem: Too many changes at once make troubleshooting difficult
Solution: Enable evolution first, add auto-optimization later
Example: Production deployment with phased feature activation
```

#### Why These Toggles Are Essential (Not Unnecessary)

**1. Operational Control** 🎛️
Even the most advanced features need operational controls:
- Netflix can disable recommendation algorithms during incidents
- Google can disable auto-scaling during maintenance
- AWS allows disabling auto-recovery for debugging

**2. Graceful Degradation** 🛡️
```
Normal Operation: Evolution + Auto-Optimization ON
High Load: Auto-Optimization OFF (reduce processing)
Critical Issue: Both OFF (minimal processing, maximum stability)
```

**3. User Trust & Transparency** 🤝
Users feel more confident when they can:
- See what the system is doing
- Control advanced features
- Disable features if something goes wrong

**4. Different Use Cases** 🎭
```
Production System: Both ON (maximum intelligence)
Development Environment: Evolution OFF (predictable testing)
Demo Environment: Both ON (show full capabilities)
Backup System: Both OFF (preserve exact state)
```

#### Recommended Settings by Use Case

**For Most Users (Recommended)**:
```
Enable Evolution: ON (always)
Auto-Optimization: ON (always)
Result: Maximum intelligence and performance
```

**For Advanced Users**:
```
Enable Evolution: ON (turn off only for specific operations)
Auto-Optimization: ON (turn off for manual control)
Result: Intelligent with operational flexibility
```

**For Developers**:
```
Enable Evolution: OFF (during development)
Auto-Optimization: OFF (during development)
Result: Predictable testing environment
```

**For Production Systems**:
```
Enable Evolution: ON (with monitoring)
Auto-Optimization: ON (with scheduled maintenance windows)
Result: Optimal performance with operational safety
```

### Accessing Evolution Intelligence Settings

#### Via Web Dashboard
1. Navigate to `http://localhost:3000/settings/evolution`
2. Go to the **Overview** tab
3. Locate the "Evolution Intelligence" section
4. Use the toggles to enable/disable features
5. Click "Save All" to persist changes

#### Via API
```bash
# Get current settings
curl -s "http://localhost:8765/api/v1/evolution-config/settings" | jq '.'

# Enable both features
curl -s -X PUT "http://localhost:8765/api/v1/evolution-config/settings" \
  -H "Content-Type: application/json" \
  -d '{"evolution_enabled": true, "auto_optimization": true}'

# Disable auto-optimization only
curl -s -X PUT "http://localhost:8765/api/v1/evolution-config/settings" \
  -H "Content-Type: application/json" \
  -d '{"auto_optimization": false}'
```

### Testing Evolution Intelligence

#### Quick Functionality Test

**Step 1: Verify Settings Persistence**
1. Go to `/settings/evolution`
2. Turn ON "Auto-Optimization"
3. Click "Save All"
4. Refresh the page (F5)
5. Verify toggle stays ON ✅

**Step 2: Test Evolution Processing**
```bash
# Trigger a test evolution operation
curl -s -X POST "http://localhost:8765/api/v1/evolution-config/test/trigger-evolution" | jq '.'

# Expected response:
{
  "success": true,
  "message": "Evolution operation completed successfully",
  "result": {
    "operation_id": "evo_1234567890",
    "operation_type": "ADD|UPDATE|DELETE|NOOP",
    "processing_time_ms": 150,
    "confidence_score": 0.85,
    "memory_affected": "memory_1234",
    "reasoning": "Applied operation based on evolution intelligence analysis",
    "auto_optimization_applied": true,
    "timestamp": 1234567890.123
  }
}
```

#### Memory Prompt Examples for Testing

**Example 1: Testing ADD Operations**
Create a conversation that should generate new memories:

```
User: "I just learned that Python 3.12 introduced a new syntax for type hints using the 'type' keyword. For example: type Point = tuple[float, float]. This is much cleaner than using TypeAlias from typing_extensions."

Expected Evolution: ADD operation creating a new memory about Python 3.12 type syntax
```

**Example 2: Testing UPDATE Operations**
First add a memory, then provide updated information:

```
Initial: "I'm working on a React project using version 17"
Later: "I upgraded my React project to version 18 and now I'm using the new concurrent features like Suspense and automatic batching"

Expected Evolution: UPDATE operation modifying the existing React project memory
```

**Example 3: Testing DELETE Operations**
Add conflicting or outdated information:

```
Initial: "My favorite code editor is Sublime Text"
Later: "I've completely switched to VS Code and no longer use Sublime Text. VS Code has better extensions and integrated terminal"

Expected Evolution: DELETE operation removing the outdated Sublime Text preference
```

**Example 4: Testing NOOP Operations**
Provide information that doesn't require memory changes:

```
Existing Memory: "I prefer TypeScript over JavaScript for large projects"
New Input: "TypeScript really helps with large codebases due to its type safety"

Expected Evolution: NOOP operation (information already captured adequately)
```

#### Advanced Testing Scenarios

**Scenario 1: Auto-Optimization Impact**
```bash
# Test with auto-optimization OFF
curl -s -X PUT "http://localhost:8765/api/v1/evolution-config/settings" \
  -H "Content-Type: application/json" \
  -d '{"auto_optimization": false}'

# Trigger evolution
curl -s -X POST "http://localhost:8765/api/v1/evolution-config/test/trigger-evolution" | jq '.result.auto_optimization_applied'
# Expected: false

# Test with auto-optimization ON
curl -s -X PUT "http://localhost:8765/api/v1/evolution-config/settings" \
  -H "Content-Type: application/json" \
  -d '{"auto_optimization": true}'

# Trigger evolution
curl -s -X POST "http://localhost:8765/api/v1/evolution-config/test/trigger-evolution" | jq '.result.auto_optimization_applied'
# Expected: true
```

**Scenario 2: Processing Statistics Monitoring**
```bash
# View current processing stats
curl -s "http://localhost:8765/api/v1/evolution-config/settings/processing-stats" | jq '.'

# Expected response:
{
  "total_operations_today": 247,
  "successful_operations": 234,
  "failed_operations": 13,
  "success_rate": 94.7,
  "average_processing_time_ms": 156,
  "queue_size": 15,
  "auto_optimization_active": true,
  "last_optimization": "2024-01-15T14:30:00Z",
  "optimizations_performed_today": 8
}
```

**Scenario 3: Settings Persistence Across Restarts**
```bash
# Set specific configuration
curl -s -X PUT "http://localhost:8765/api/v1/evolution-config/settings" \
  -H "Content-Type: application/json" \
  -d '{"evolution_enabled": true, "auto_optimization": true}'

# Restart the container
docker restart memory-mcp

# Wait for startup
sleep 10

# Verify settings persisted
curl -s "http://localhost:8765/api/v1/evolution-config/settings" | jq '{evolution_enabled, auto_optimization}'
# Expected: Both should be true
```

### Monitoring Evolution Intelligence

#### Real-Time Monitoring via Web Dashboard

**Evolution Dashboard** (`/evolution`):
1. **Key Metrics Display**: Learning efficiency, conflict resolution, memory quality
2. **Evolution Timeline**: Visual chart showing ADD/UPDATE/DELETE/NOOP trends
3. **Operation Breakdown**: Pie chart with operation type distribution
4. **Real-Time Activity Feed**: Live stream of evolution operations

**System Status Monitoring** (`/settings/evolution`):
1. **Component Health**: Memory Engine, Vector Store, Evolution Service, Prompt System
2. **Click for Details**: Click any warning/error status for detailed recovery instructions
3. **Auto-Recovery**: Built-in recovery actions for common issues

#### API Monitoring Endpoints

**System Health**:
```bash
# Overall system health
curl -s "http://localhost:8765/api/v1/health/" | jq '.status'

# Evolution service specific health
curl -s "http://localhost:8765/api/v1/health/evolution-service" | jq '.'
```

**Processing Metrics**:
```bash
# Current processing statistics
curl -s "http://localhost:8765/api/v1/evolution-config/settings/processing-stats" | jq '.'

# Evolution analytics (if available)
curl -s "http://localhost:8765/api/v1/evolution-config/metrics?timeframe=week" | jq '.'
```

#### Key Metrics to Monitor

**Performance Indicators**:
- **Success Rate**: Should be > 90%
- **Processing Time**: Should be < 200ms average
- **Queue Size**: Should be manageable (< 100 for normal load)
- **Learning Efficiency**: Measures how well the system improves over time

**Health Indicators**:
- **Memory Engine**: Should be "healthy"
- **Vector Store**: Should be "healthy"
- **Evolution Service**: May show "warning" (this is intentional for demo)
- **Prompt System**: Should be "healthy"

### Troubleshooting Evolution Intelligence

#### Common Issues and Solutions

**Issue 1: Settings Not Persisting**
```
Symptoms: Toggles reset after page refresh
Diagnosis: Configuration service not saving properly
Solution:
1. Check container logs: docker logs memory-mcp
2. Verify config directory: docker exec memory-mcp ls -la /app/data/config/
3. Check file permissions: docker exec memory-mcp ls -la /app/data/config/evolution_settings.json
4. Restart container: docker restart memory-mcp
```

**Issue 2: Evolution Operations Failing**
```
Symptoms: Test evolution returns errors
Diagnosis: Evolution service not functioning
Solution:
1. Check if evolution is enabled: curl -s "http://localhost:8765/api/v1/evolution-config/settings" | jq '.evolution_enabled'
2. Verify system health: curl -s "http://localhost:8765/api/v1/health/evolution-service"
3. Check processing stats: curl -s "http://localhost:8765/api/v1/evolution-config/settings/processing-stats"
4. Try recovery action via UI or API
```

**Issue 3: Auto-Optimization Not Working**
```
Symptoms: auto_optimization_applied always false
Diagnosis: Auto-optimization disabled or not functioning
Solution:
1. Verify auto-optimization is enabled: curl -s "http://localhost:8765/api/v1/evolution-config/settings" | jq '.auto_optimization'
2. Check that evolution is also enabled (prerequisite)
3. Monitor processing stats for optimization activity
4. Test with known optimization scenarios
```

**Issue 4: High Resource Usage**
```
Symptoms: System running slowly, high CPU/memory usage
Diagnosis: Evolution processing consuming too many resources
Solution:
1. Temporarily disable auto-optimization: Keep evolution ON, turn auto-optimization OFF
2. Monitor resource usage: docker stats memory-mcp
3. Check processing queue size: Look for queue_size in processing stats
4. Consider adjusting processing intervals in advanced settings
```

#### Recovery Actions

**Automatic Recovery** (via UI):
1. Go to `/settings/evolution`
2. Click on any component showing warning/error status
3. Click "Attempt Auto-Recovery" button
4. Monitor progress and check if issue resolves

**Manual Recovery** (via API):
```bash
# Attempt recovery for specific component
curl -s -X POST "http://localhost:8765/api/v1/health/recovery/evolution_service"

# Reset settings to defaults
curl -s -X POST "http://localhost:8765/api/v1/evolution-config/settings/reset"

# Restart evolution processing
docker restart memory-mcp
```

### Best Practices for Evolution Intelligence

#### Optimal Configuration

**For Production Use**:
```json
{
  "evolution_enabled": true,
  "auto_optimization": true,
  "advanced_settings": {
    "processing_interval_seconds": 30,
    "batch_size": 10,
    "quality_threshold": 0.8,
    "conflict_resolution_strategy": "merge"
  }
}
```

**For Development Use**:
```json
{
  "evolution_enabled": false,
  "auto_optimization": false,
  "note": "Disabled for predictable testing environment"
}
```

#### Memory Content Guidelines for Evolution

**Good Memory Content** (helps evolution work better):
```
✅ "I learned that React 18 introduced automatic batching for better performance"
✅ "My current project uses PostgreSQL 14 with the new multirange types"
✅ "I prefer using TypeScript strict mode for better type safety"
```

**Poor Memory Content** (harder for evolution to process):
```
❌ "Stuff happened today"
❌ "That thing we discussed"
❌ "Random thoughts and ideas"
```

#### Monitoring Schedule

**Daily Monitoring**:
- Check processing success rate
- Monitor queue size
- Review any error notifications

**Weekly Monitoring**:
- Analyze evolution trends
- Review memory quality improvements
- Check system resource usage

**Monthly Monitoring**:
- Evaluate learning efficiency metrics
- Review and update custom prompts if needed
- Assess overall system performance improvements

#### Performance Optimization Tips

**For Better Evolution Performance**:
1. **Clear Memory Descriptions**: Help the AI understand context better
2. **Consistent Terminology**: Use consistent terms across memories
3. **Regular Cleanup**: Remove test or temporary memories
4. **Monitor Resource Usage**: Keep an eye on system performance

**For Better Auto-Optimization**:
1. **Enable During Off-Peak Hours**: If resource usage is a concern
2. **Monitor Optimization Results**: Check if optimizations improve performance
3. **Gradual Rollout**: Enable for one application at a time initially
4. **Regular Backups**: Evolution creates automatic backups, but verify they exist

### Configuration Files and Persistence

#### Configuration File Location
```bash
# Main configuration file
/app/data/config/evolution_settings.json

# Backup files
/app/data/config/backups/evolution_settings_YYYYMMDD_HHMMSS.json
```

#### Configuration File Structure
```json
{
  "evolution_enabled": true,
  "auto_optimization": false,
  "system_status": {
    "memory_engine": "healthy",
    "vector_store": "healthy",
    "evolution_service": "warning",
    "prompt_system": "healthy"
  },
  "advanced_settings": {
    "processing_interval_seconds": 30,
    "batch_size": 10,
    "quality_threshold": 0.8,
    "conflict_resolution_strategy": "merge",
    "auto_backup_enabled": true,
    "max_queue_size": 1000
  },
  "metadata": {
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T14:30:00Z",
    "version": "1.0.0"
  }
}
```

#### Backup and Recovery
```bash
# View available backups
docker exec memory-mcp ls -la /app/data/config/backups/

# Manually create backup
curl -s -X POST "http://localhost:8765/api/v1/evolution-config/settings/backup"

# Restore from backup (if needed)
docker exec memory-mcp cp /app/data/config/backups/evolution_settings_20240115_143000.json /app/data/config/evolution_settings.json
docker restart memory-mcp
```

This comprehensive documentation provides everything you need to understand, configure, test, and troubleshoot the Evolution Intelligence features in Memory Master v2.

## Best Practices

### Memory Content Guidelines

**Effective Memories**:
- Be specific and contextual
- Include temporal markers ("Today", "In project X")
- Link to actions and consequences
- Use relevant metadata tags

**Example Good Memory**:
```
"In the Memory Master v2 project, I discovered that using connection pooling with SQLAlchemy reduced database query time by 40%. This was implemented in the database service layer."
```

**Example Poor Memory**:
```
"Database optimization is good."
```

### Application Organization

**Separate by Context**:
- Create different apps for different projects
- Use descriptive names (e.g., "Project-Alpha-Claude", "Personal-VS-Code")
- Configure appropriate permissions per app

**Memory Hygiene**:
- Regularly review and clean up outdated memories
- Use the evolution intelligence to maintain quality
- Archive old project memories instead of deleting

### Performance Optimization

**For Best Performance**:
- Keep individual memories under 2000 words
- Use relevant metadata for faster filtering
- Regularly monitor system health
- Enable caching for frequently accessed memories

## Troubleshooting

### Common Issues

**Memory Not Saving**:
1. Check OpenAI API key configuration
2. Verify Qdrant vector store connectivity
3. Ensure sufficient disk space
4. Check application permissions

**Search Not Working**:
1. Verify vector embeddings are generated
2. Check search query format
3. Ensure memories are in "active" state
4. Test with simpler search terms

**MCP Connection Failed**:
1. Verify API server is running (http://localhost:8765)
2. Check MCP server configuration
3. Restart the client application
4. Review API server logs

**Slow Performance**:
1. Check system resource usage
2. Optimize database queries
3. Clear old cached data
4. Reduce concurrent operations

### Getting Help

**Log Files**:
- API Server: `docker-compose logs openmemory-mcp`
- UI: `docker-compose logs openmemory-ui`
- Vector Store: `docker-compose logs mem0_store`

**Health Checks**:
- System Health: http://localhost:8765/health
- Qdrant Status: http://localhost:6333/dashboard
- Database Connection: Check API logs

**Support Resources**:
- API Documentation: http://localhost:8765/docs
- System Metrics: Web dashboard analytics
- Developer Manual: See DeveloperManual.md

---

This user manual covers the essential operations for Memory Master v2. For technical details and development information, refer to the DeveloperManual.md.
