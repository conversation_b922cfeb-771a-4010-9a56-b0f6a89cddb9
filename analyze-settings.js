const fs = require('fs');
const path = require('path');

function analyzeSettingsHTML() {
  console.log('📋 SETTINGS PAGES ANALYSIS REPORT');
  console.log('='.repeat(80));
  
  const htmlContent = fs.readFileSync('/tmp/settings.html', 'utf-8');
  
  const findings = {
    navigation: [],
    forms: [],
    inputs: [],
    buttons: [],
    content: [],
    errors: [],
    recommendations: []
  };

  // Extract navigation elements
  const navLinks = htmlContent.match(/href="[^"]*"/g) || [];
  const uniqueLinks = [...new Set(navLinks.map(link => link.replace(/href="|"/g, '')))];
  findings.navigation = uniqueLinks.filter(link => link.startsWith('/'));
  
  console.log('\n🔍 NAVIGATION ANALYSIS:');
  console.log(`Found ${findings.navigation.length} navigation links:`);
  findings.navigation.forEach((link, index) => {
    console.log(`${index + 1}. ${link}`);
  });
  
  // Check for evolution settings specifically
  const hasEvolutionLink = findings.navigation.some(link => link.includes('evolution'));
  if (hasEvolutionLink) {
    console.log('✅ Evolution settings link found in navigation');
  } else {
    console.log('❌ Evolution settings link NOT found in navigation');
    findings.errors.push('Evolution settings link not found in navigation');
  }

  // Extract form elements
  const formMatches = htmlContent.match(/<form[^>]*>[\s\S]*?<\/form>/g) || [];
  findings.forms = formMatches.length;
  
  console.log('\n🔍 FORMS ANALYSIS:');
  console.log(`Found ${findings.forms} forms`);
  
  if (findings.forms === 0) {
    console.log('❌ No forms found - this might indicate the page is not fully loaded or forms are dynamically generated');
    findings.errors.push('No forms found on the page');
  }

  // Extract input elements
  const inputMatches = htmlContent.match(/<input[^>]*>/g) || [];
  const textareaMatches = htmlContent.match(/<textarea[^>]*>/g) || [];
  const selectMatches = htmlContent.match(/<select[^>]*>/g) || [];
  
  findings.inputs = inputMatches.length + textareaMatches.length + selectMatches.length;
  
  console.log('\n🔍 INPUT ELEMENTS ANALYSIS:');
  console.log(`Found ${inputMatches.length} input elements`);
  console.log(`Found ${textareaMatches.length} textarea elements`);
  console.log(`Found ${selectMatches.length} select elements`);
  console.log(`Total interactive elements: ${findings.inputs}`);
  
  // Extract specific input details
  const inputDetails = [];
  inputMatches.forEach(input => {
    const idMatch = input.match(/id="([^"]*)"/);
    const typeMatch = input.match(/type="([^"]*)"/);
    const placeholderMatch = input.match(/placeholder="([^"]*)"/);
    
    if (idMatch) {
      inputDetails.push({
        id: idMatch[1],
        type: typeMatch ? typeMatch[1] : 'text',
        placeholder: placeholderMatch ? placeholderMatch[1] : ''
      });
    }
  });
  
  console.log('\n📝 INPUT DETAILS:');
  inputDetails.forEach((input, index) => {
    console.log(`${index + 1}. ID: ${input.id}, Type: ${input.type}, Placeholder: ${input.placeholder}`);
  });
  
  // Extract button elements
  const buttonMatches = htmlContent.match(/<button[^>]*>[\s\S]*?<\/button>/g) || [];
  findings.buttons = buttonMatches.length;
  
  console.log('\n🔍 BUTTONS ANALYSIS:');
  console.log(`Found ${findings.buttons} buttons`);
  
  const buttonTexts = [];
  buttonMatches.forEach(button => {
    // Extract text content from button (simplified)
    const textMatch = button.match(/>([^<]*Save[^<]*)</i) || 
                      button.match(/>([^<]*Reset[^<]*)</i) || 
                      button.match(/>([^<]*Settings[^<]*)</i);
    if (textMatch) {
      buttonTexts.push(textMatch[1].trim());
    }
  });
  
  console.log('Button texts found:');
  buttonTexts.forEach((text, index) => {
    console.log(`${index + 1}. ${text}`);
  });
  
  // Check for key functionality
  const hasSaveButton = htmlContent.includes('Save Settings') || htmlContent.includes('Save');
  const hasResetButton = htmlContent.includes('Reset') || htmlContent.includes('Reset to Defaults');
  
  if (hasSaveButton) {
    console.log('✅ Save functionality found');
  } else {
    console.log('❌ Save functionality NOT found');
    findings.errors.push('Save functionality not found');
  }
  
  if (hasResetButton) {
    console.log('✅ Reset functionality found');
  } else {
    console.log('❌ Reset functionality NOT found');
    findings.errors.push('Reset functionality not found');
  }

  // Extract content sections
  const h1Matches = htmlContent.match(/<h1[^>]*>([^<]*)<\/h1>/g) || [];
  const h2Matches = htmlContent.match(/<h2[^>]*>([^<]*)<\/h2>/g) || [];
  const h3Matches = htmlContent.match(/<h3[^>]*>([^<]*)<\/h3>/g) || [];
  
  console.log('\n🔍 CONTENT STRUCTURE:');
  console.log(`Found ${h1Matches.length} H1 headings`);
  console.log(`Found ${h2Matches.length} H2 headings`);
  console.log(`Found ${h3Matches.length} H3 headings`);
  
  // Extract main headings
  const headings = [];
  h1Matches.forEach(h1 => {
    const textMatch = h1.match(/>([^<]*)</);
    if (textMatch) headings.push(`H1: ${textMatch[1]}`);
  });
  h2Matches.forEach(h2 => {
    const textMatch = h2.match(/>([^<]*)</);
    if (textMatch) headings.push(`H2: ${textMatch[1]}`);
  });
  h3Matches.forEach(h3 => {
    const textMatch = h3.match(/>([^<]*)</);
    if (textMatch) headings.push(`H3: ${textMatch[1]}`);
  });
  
  console.log('Headings found:');
  headings.forEach((heading, index) => {
    console.log(`${index + 1}. ${heading}`);
  });

  // Check for system status
  const hasSystemStatus = htmlContent.includes('System Status') || htmlContent.includes('Healthy');
  const hasConfiguration = htmlContent.includes('Configuration') || htmlContent.includes('Basic Configuration');
  
  if (hasSystemStatus) {
    console.log('✅ System status section found');
  } else {
    console.log('❌ System status section NOT found');
    findings.errors.push('System status section not found');
  }
  
  if (hasConfiguration) {
    console.log('✅ Configuration section found');
  } else {
    console.log('❌ Configuration section NOT found');
    findings.errors.push('Configuration section not found');
  }

  // Test evolution settings access
  console.log('\n🔍 EVOLUTION SETTINGS ACCESS TEST:');
  console.log('Testing /settings/evolution endpoint...');
  
  // This would need to be done with a proper HTTP client
  console.log('⚠️  Evolution settings access needs to be tested with live requests');
  findings.recommendations.push('Test evolution settings endpoint with proper HTTP client');

  // Generate recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  
  if (findings.forms === 0) {
    findings.recommendations.push('Investigate why forms are not appearing - may be client-side rendering issue');
  }
  
  if (findings.inputs < 3) {
    findings.recommendations.push('Expected more input fields for a settings page');
  }
  
  if (!hasEvolutionLink) {
    findings.recommendations.push('Add navigation link to evolution settings');
  }
  
  findings.recommendations.forEach((rec, index) => {
    console.log(`${index + 1}. ${rec}`);
  });

  // Summary
  console.log('\n📊 SUMMARY:');
  console.log(`Navigation links: ${findings.navigation.length}`);
  console.log(`Forms: ${findings.forms}`);
  console.log(`Input elements: ${findings.inputs}`);
  console.log(`Buttons: ${findings.buttons}`);
  console.log(`Errors found: ${findings.errors.length}`);
  console.log(`Recommendations: ${findings.recommendations.length}`);
  
  if (findings.errors.length > 0) {
    console.log('\n🚨 ERRORS:');
    findings.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('Analysis completed!');
  console.log('='.repeat(80));
  
  return findings;
}

// Run the analysis
if (require.main === module) {
  analyzeSettingsHTML();
}

module.exports = { analyzeSettingsHTML };