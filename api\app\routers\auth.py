from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.database import get_db
from app.models import User
from app.auth.middleware import get_current_user, require_authentication, AuthenticatedUser, DefaultUser
from app.auth.supabase import supabase_client
from pydantic import BaseModel
from typing import Union, Optional, List
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["authentication"])

class UserResponse(BaseModel):
    user_id: str
    email: str
    name: Optional[str] = None
    email_verified: bool = False
    is_authenticated: bool = False
    supabase_user_id: Optional[str] = None
    created_at: Optional[str] = None
    last_sign_in_at: Optional[str] = None

    class Config:
        from_attributes = True

class AuthStatusResponse(BaseModel):
    auth_enabled: bool
    is_authenticated: bool
    user: Optional[UserResponse] = None

@router.get("/status", response_model=AuthStatusResponse)
async def get_auth_status(
    current_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get current authentication status and user information.
    """
    try:
        # Get user from database
        user_data = None
        if current_user.is_authenticated:
            db_user = db.query(User).filter(User.user_id == current_user.user_id).first()
            if db_user:
                user_data = UserResponse(
                    user_id=db_user.user_id,
                    email=db_user.email or "",
                    name=db_user.name,
                    email_verified=db_user.email_verified or False,
                    is_authenticated=True,
                    supabase_user_id=str(db_user.supabase_user_id) if db_user.supabase_user_id else None,
                    created_at=db_user.created_at.isoformat() if db_user.created_at else None,
                    last_sign_in_at=db_user.last_sign_in_at.isoformat() if db_user.last_sign_in_at else None
                )
        else:
            # For default user
            user_data = UserResponse(
                user_id=current_user.user_id,
                email=current_user.email,
                name="Default User",
                email_verified=False,
                is_authenticated=False
            )

        return AuthStatusResponse(
            auth_enabled=supabase_client.auth_enabled,
            is_authenticated=current_user.is_authenticated,
            user=user_data
        )
    except Exception as e:
        logger.error(f"Error getting auth status: {e}")
        return AuthStatusResponse(
            auth_enabled=supabase_client.auth_enabled,
            is_authenticated=False,
            user=None
        )

@router.get("/profile", response_model=UserResponse)
async def get_user_profile(
    current_user: AuthenticatedUser = Depends(require_authentication),
    db: Session = Depends(get_db)
):
    """
    Get the current user's profile. Requires authentication.
    """
    try:
        db_user = db.query(User).filter(User.user_id == current_user.user_id).first()
        
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )

        return UserResponse(
            user_id=db_user.user_id,
            email=db_user.email or "",
            name=db_user.name,
            email_verified=db_user.email_verified or False,
            is_authenticated=True,
            supabase_user_id=str(db_user.supabase_user_id) if db_user.supabase_user_id else None,
            created_at=db_user.created_at.isoformat() if db_user.created_at else None,
            last_sign_in_at=db_user.last_sign_in_at.isoformat() if db_user.last_sign_in_at else None
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

class UpdateProfileRequest(BaseModel):
    name: Optional[str] = None

class UserListResponse(BaseModel):
    users: List[UserResponse]
    total: int

@router.put("/profile", response_model=UserResponse)
async def update_user_profile(
    request: UpdateProfileRequest,
    current_user: AuthenticatedUser = Depends(require_authentication),
    db: Session = Depends(get_db)
):
    """
    Update the current user's profile. Requires authentication.
    """
    try:
        db_user = db.query(User).filter(User.user_id == current_user.user_id).first()
        
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )

        # Update fields
        if request.name is not None:
            db_user.name = request.name

        db.commit()
        db.refresh(db_user)

        return UserResponse(
            user_id=db_user.user_id,
            email=db_user.email or "",
            name=db_user.name,
            email_verified=db_user.email_verified or False,
            is_authenticated=True,
            supabase_user_id=str(db_user.supabase_user_id) if db_user.supabase_user_id else None,
            created_at=db_user.created_at.isoformat() if db_user.created_at else None,
            last_sign_in_at=db_user.last_sign_in_at.isoformat() if db_user.last_sign_in_at else None
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user profile: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.get("/users", response_model=UserListResponse)
async def list_users(
    db: Session = Depends(get_db)
):
    """
    Get a list of all users in the system.
    """
    try:
        users = db.query(User).order_by(User.created_at.desc()).all()
        
        user_responses = []
        for user in users:
            user_responses.append(UserResponse(
                user_id=user.user_id,
                email=user.email or "",
                name=user.name or user.user_id,
                email_verified=user.email_verified or False,
                is_authenticated=False,  # This is just for listing, not current auth status
                supabase_user_id=str(user.supabase_user_id) if user.supabase_user_id else None,
                created_at=user.created_at.isoformat() if user.created_at else None,
                last_sign_in_at=user.last_sign_in_at.isoformat() if user.last_sign_in_at else None
            ))
        
        return UserListResponse(
            users=user_responses,
            total=len(user_responses)
        )
    except Exception as e:
        logger.error(f"Error listing users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )