from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from sqlalchemy import func, and_
from typing import Union

from ..database import get_db
from ..models import EvolutionOperation, User
from ..auth.middleware import get_current_user, Authenticated<PERSON><PERSON>, Default<PERSON>ser
from ..config import USER_ID

router = APIRouter(prefix="/evolution", tags=["evolution"])


@router.get("/status")
async def get_evolution_status(
    user_id: str = None,
    auth_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get simplified evolution status for dashboard"""
    
    # Use provided user_id or fall back to configured MCP user
    target_user_id = user_id if user_id else USER_ID
    
    user = db.query(User).filter(User.user_id == target_user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail=f"User '{target_user_id}' not found")
    
    # Get today's date range
    today = datetime.now().date()
    today_start = datetime.combine(today, datetime.min.time())
    today_end = datetime.combine(today, datetime.max.time())
    
    # Get total operations count for this user
    total_ops = db.query(func.count(EvolutionOperation.id)).filter(
        EvolutionOperation.user_id == user.id
    ).scalar() or 0
    
    # Get today's operations count for this user
    today_ops = db.query(func.count(EvolutionOperation.id)).filter(
        and_(
            EvolutionOperation.user_id == user.id,
            EvolutionOperation.created_at >= today_start,
            EvolutionOperation.created_at <= today_end
        )
    ).scalar() or 0
    
    # Get operation type breakdown for this user's operations
    operation_counts = db.query(
        EvolutionOperation.operation_type,
        func.count(EvolutionOperation.id)
    ).filter(EvolutionOperation.user_id == user.id).group_by(EvolutionOperation.operation_type).all()
    
    # Initialize counts
    counts = {'add': 0, 'update': 0, 'delete': 0, 'noop': 0}
    for op_type, count in operation_counts:
        op_type_str = op_type.value.lower() if hasattr(op_type, 'value') else str(op_type).lower()
        if op_type_str in counts:
            counts[op_type_str] = count
    
    # Get last operation timestamp for this user
    last_op = db.query(EvolutionOperation).filter(
        EvolutionOperation.user_id == user.id
    ).order_by(EvolutionOperation.created_at.desc()).first()
    
    # Determine system status (simplified logic)
    status = "healthy"
    if total_ops == 0:
        status = "error"
    elif today_ops == 0 and datetime.now().hour > 6:  # No ops today after 6 AM
        status = "degraded"
    
    return {
        "status": status,
        "totalOperations": total_ops,
        "todayOperations": today_ops,
        "lastOperation": last_op.created_at.isoformat() if last_op else None,
        "operationCounts": counts
    }