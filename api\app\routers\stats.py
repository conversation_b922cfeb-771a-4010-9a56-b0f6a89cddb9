from fastapi import API<PERSON>outer, Depends, HTTPException
from sqlalchemy.orm import Session
from app.database import get_db
from app.models import User, Memory, App, MemoryState
from app.auth.middleware import get_current_user, AuthenticatedUser, DefaultUser
from app.config import USER_ID
from typing import Union


router = APIRouter(prefix="/api/v1/stats", tags=["stats"])

@router.get("")
async def get_profile(
    user_id: str = None,
    auth_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Use provided user_id or fall back to configured MCP user
    target_user_id = user_id if user_id else USER_ID
    
    user = db.query(User).filter(User.user_id == target_user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail=f"User '{target_user_id}' not found in database")
    
    # Get total number of memories
    total_memories = db.query(Memory).filter(Memory.user_id == user.id, Memory.state != MemoryState.deleted.value).count()

    # Get total number of distinct apps that have memories (connected apps)
    total_apps = db.query(Memory.app_id).filter(
        Memory.user_id == user.id,
        Memory.state != MemoryState.deleted.value
    ).distinct().count()

    # Get all apps for the apps list (keeping this for compatibility)
    apps = db.query(App).filter(App.owner_id == user.id)

    return {
        "total_memories": total_memories,
        "total_apps": total_apps,
        "apps": apps.all()
    }

