import pytest
from fastapi.testclient import TestClient
from main import app


client = TestClient(app)


def test_evolution_status_endpoint_exists():
    """Test that the evolution status endpoint exists and returns correct format"""
    response = client.get("/api/v1/evolution/status")
    assert response.status_code == 200
    
    data = response.json()
    
    # Check required fields exist
    assert "status" in data
    assert "totalOperations" in data
    assert "todayOperations" in data
    assert "lastOperation" in data
    assert "operationCounts" in data
    
    # Check status is valid
    assert data["status"] in ["healthy", "degraded", "error"]
    
    # Check numeric fields are integers
    assert isinstance(data["totalOperations"], int)
    assert isinstance(data["todayOperations"], int)
    assert data["totalOperations"] >= 0
    assert data["todayOperations"] >= 0
    
    # Check operation counts structure
    assert "add" in data["operationCounts"]
    assert "update" in data["operationCounts"]
    assert "delete" in data["operationCounts"]
    assert "noop" in data["operationCounts"]
    
    # Check operation counts are integers
    for op_type in ["add", "update", "delete", "noop"]:
        assert isinstance(data["operationCounts"][op_type], int)
        assert data["operationCounts"][op_type] >= 0


def test_evolution_status_response_format():
    """Test that the response format matches expected schema"""
    response = client.get("/api/v1/evolution/status")
    assert response.status_code == 200
    
    data = response.json()
    
    # Test specific field constraints
    assert data["status"] in ["healthy", "degraded", "error"]
    assert isinstance(data["totalOperations"], int)
    assert isinstance(data["todayOperations"], int)
    
    # lastOperation should be None or a valid ISO timestamp
    if data["lastOperation"] is not None:
        assert isinstance(data["lastOperation"], str)
        # Basic check for ISO format (contains T and timezone info)
        assert "T" in data["lastOperation"]
    
    # Operation counts should sum to totalOperations (or be less due to potential DB inconsistencies)
    total_counted = sum(data["operationCounts"].values())
    assert total_counted <= data["totalOperations"]  # Allow for potential inconsistencies