const { chromium } = require('playwright');

async function basicTest() {
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  console.log('Testing basic page load...');
  
  try {
    await page.goto('http://localhost:3000/memories');
    
    console.log('Page loaded. Title:', await page.title());
    console.log('URL:', await page.url());
    
    // Quick element counts
    const buttonCount = await page.locator('button').count();
    console.log('Button count:', buttonCount);
    
    const inputCount = await page.locator('input').count();
    console.log('Input count:', inputCount);
    
    // Check for specific text
    const hasMemories = await page.locator('text=Memories').count();
    console.log('Has "Memories" text:', hasMemories > 0);
    
    const hasCreate = await page.locator('text=Create Memory').count();
    console.log('Has "Create Memory" text:', hasCreate > 0);
    
    await browser.close();
    console.log('Test completed successfully!');
    
  } catch (error) {
    console.error('Test failed:', error.message);
    await browser.close();
  }
}

basicTest();