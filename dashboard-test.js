const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

// Create test results directory
const resultsDir = './test-results';
if (!fs.existsSync(resultsDir)) {
  fs.mkdirSync(resultsDir, { recursive: true });
}

test.describe('Dashboard Page Tests', () => {
  let consoleMessages = [];
  let errors = [];

  test.beforeEach(async ({ page }) => {
    // Clear previous test data
    consoleMessages = [];
    errors = [];

    // Listen for console messages
    page.on('console', msg => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toISOString()
      });
    });

    // Listen for page errors
    page.on('pageerror', err => {
      errors.push({
        message: err.message,
        stack: err.stack,
        timestamp: new Date().toISOString()
      });
    });

    // Set longer timeout for page loads
    page.setDefaultTimeout(30000);
  });

  test('Dashboard page comprehensive test', async ({ page }) => {
    console.log('Starting dashboard comprehensive test...');
    
    try {
      // 1. Navigate to dashboard
      console.log('1. Navigating to http://localhost:3000...');
      await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
      
      // Take initial screenshot
      await page.screenshot({ 
        path: path.join(resultsDir, '01-initial-load.png'),
        fullPage: true 
      });
      console.log('✓ Initial screenshot taken');

      // 2. Verify page loads correctly
      console.log('2. Verifying page loads correctly...');
      await expect(page).toHaveTitle(/Memory Master|Dashboard/i);
      console.log('✓ Page title verified');

      // Wait for any loading states to complete
      await page.waitForTimeout(2000);

      // 3. Check for navigation menu
      console.log('3. Checking navigation menu...');
      const navbar = page.locator('nav, [role="navigation"], .navbar, header');
      await expect(navbar).toBeVisible();
      
      await page.screenshot({ 
        path: path.join(resultsDir, '02-navigation-menu.png'),
        fullPage: true 
      });
      console.log('✓ Navigation menu is present');

      // 4. Test navigation links
      console.log('4. Testing navigation functionality...');
      const navLinks = page.locator('nav a, header a').filter({ hasText: /memories|dashboard|settings|evolution/i });
      const navCount = await navLinks.count();
      console.log(`Found ${navCount} navigation links`);

      for (let i = 0; i < Math.min(navCount, 3); i++) {
        const link = navLinks.nth(i);
        const linkText = await link.textContent();
        console.log(`Testing navigation link: ${linkText}`);
        
        if (linkText && !linkText.toLowerCase().includes('logout')) {
          await link.click();
          await page.waitForTimeout(1000);
          await page.screenshot({ 
            path: path.join(resultsDir, `03-nav-${i}-${linkText.replace(/[^a-zA-Z0-9]/g, '')}.png`),
            fullPage: true 
          });
        }
      }

      // Return to dashboard
      await page.goto('http://localhost:3000');
      console.log('✓ Navigation functionality tested');

      // 5. Test Create Memory button
      console.log('5. Testing Create Memory button...');
      const createMemoryButton = page.locator('button, a').filter({ hasText: /create memory|add memory|new memory/i }).first();
      
      if (await createMemoryButton.isVisible()) {
        await createMemoryButton.click();
        await page.waitForTimeout(1000);
        
        await page.screenshot({ 
          path: path.join(resultsDir, '04-create-memory-modal.png'),
          fullPage: true 
        });
        
        // Try to close modal if it opened
        const closeButton = page.locator('button').filter({ hasText: /cancel|close|×/i }).first();
        if (await closeButton.isVisible()) {
          await closeButton.click();
        } else {
          await page.keyboard.press('Escape');
        }
        console.log('✓ Create Memory button tested');
      } else {
        console.log('⚠ Create Memory button not found');
      }

      // 6. Test theme toggle
      console.log('6. Testing theme toggle...');
      const themeToggle = page.locator('button').filter({ hasText: /theme|dark|light/i }).or(
        page.locator('[aria-label*="theme"]').or(
          page.locator('button[data-testid*="theme"]')
        )
      ).first();

      if (await themeToggle.isVisible()) {
        // Take screenshot before theme change
        await page.screenshot({ 
          path: path.join(resultsDir, '05-before-theme-toggle.png'),
          fullPage: true 
        });

        await themeToggle.click();
        await page.waitForTimeout(500);
        
        // Take screenshot after theme change
        await page.screenshot({ 
          path: path.join(resultsDir, '06-after-theme-toggle.png'),
          fullPage: true 
        });
        console.log('✓ Theme toggle tested');
      } else {
        console.log('⚠ Theme toggle button not found');
      }

      // 7. Test refresh button
      console.log('7. Testing refresh functionality...');
      const refreshButton = page.locator('button').filter({ hasText: /refresh|reload/i }).or(
        page.locator('[aria-label*="refresh"]')
      ).first();

      if (await refreshButton.isVisible()) {
        await refreshButton.click();
        await page.waitForTimeout(1000);
        console.log('✓ Refresh button tested');
      } else {
        console.log('⚠ Refresh button not found');
      }

      // 8. Check for stats/metrics display
      console.log('8. Checking for stats/metrics display...');
      const statsElements = page.locator('[class*="stat"], [class*="metric"], [class*="card"]').filter({ hasText: /total|count|memories|apps/i });
      const statsCount = await statsElements.count();
      
      if (statsCount > 0) {
        await page.screenshot({ 
          path: path.join(resultsDir, '07-stats-metrics.png'),
          fullPage: true 
        });
        console.log(`✓ Found ${statsCount} stats/metrics elements`);
      } else {
        console.log('⚠ No stats/metrics display found');
      }

      // 9. Final comprehensive screenshot
      await page.screenshot({ 
        path: path.join(resultsDir, '08-final-state.png'),
        fullPage: true 
      });

      console.log('Dashboard test completed successfully!');

    } catch (error) {
      console.error('Test failed:', error.message);
      await page.screenshot({ 
        path: path.join(resultsDir, '99-error-state.png'),
        fullPage: true 
      });
      throw error;
    }
  });

  test.afterEach(async () => {
    // Generate test report
    const reportData = {
      timestamp: new Date().toISOString(),
      consoleMessages: consoleMessages,
      errors: errors,
      testSummary: {
        totalConsoleMessages: consoleMessages.length,
        errorCount: errors.length,
        warningCount: consoleMessages.filter(msg => msg.type === 'warning').length,
        infoCount: consoleMessages.filter(msg => msg.type === 'info').length
      }
    };

    fs.writeFileSync(
      path.join(resultsDir, 'test-report.json'), 
      JSON.stringify(reportData, null, 2)
    );

    // Generate human-readable report
    let humanReport = `# Dashboard Test Report
Generated: ${new Date().toISOString()}

## Test Summary
- Total Console Messages: ${reportData.testSummary.totalConsoleMessages}
- JavaScript Errors: ${reportData.testSummary.errorCount}
- Console Warnings: ${reportData.testSummary.warningCount}
- Console Info Messages: ${reportData.testSummary.infoCount}

## Console Messages
${consoleMessages.map(msg => `[${msg.timestamp}] ${msg.type.toUpperCase()}: ${msg.text}`).join('\n')}

## JavaScript Errors
${errors.length > 0 ? errors.map(err => `[${err.timestamp}] ERROR: ${err.message}\nStack: ${err.stack}`).join('\n\n') : 'No JavaScript errors detected'}

## Screenshots Generated
- 01-initial-load.png: Initial page load
- 02-navigation-menu.png: Navigation menu visibility
- 03-nav-*: Navigation functionality tests
- 04-create-memory-modal.png: Create Memory button test
- 05-before-theme-toggle.png: Before theme change
- 06-after-theme-toggle.png: After theme change
- 07-stats-metrics.png: Stats/metrics display
- 08-final-state.png: Final page state
- 99-error-state.png: Error state (if any errors occurred)
`;

    fs.writeFileSync(
      path.join(resultsDir, 'dashboard-test-report.md'), 
      humanReport
    );
  });
});

// Additional utility test for API connectivity
test.describe('API Connectivity Tests', () => {
  test('API health check', async ({ page }) => {
    console.log('Testing API connectivity...');
    
    try {
      // Test API health endpoint
      const response = await page.request.get('http://localhost:8765/health');
      const healthStatus = response.ok();
      
      console.log(`API Health Status: ${healthStatus ? 'HEALTHY' : 'UNHEALTHY'}`);
      
      if (healthStatus) {
        const healthData = await response.json();
        console.log('API Health Data:', JSON.stringify(healthData, null, 2));
      }
      
      // Test MCP health if available
      try {
        const mcpResponse = await page.request.get('http://localhost:8765/mcp/claude/sse/health');
        const mcpStatus = mcpResponse.ok();
        console.log(`MCP Health Status: ${mcpStatus ? 'HEALTHY' : 'UNHEALTHY'}`);
      } catch (e) {
        console.log('MCP endpoint not available');
      }

    } catch (error) {
      console.error('API connectivity test failed:', error.message);
    }
  });
});