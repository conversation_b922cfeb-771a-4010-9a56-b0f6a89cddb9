const { chromium } = require('playwright');

async function debugApplication() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Listen for console messages
  page.on('console', msg => {
    const type = msg.type();
    if (type === 'error' || type === 'warning') {
      console.log(`🚨 [${type.toUpperCase()}] ${msg.text()}`);
    }
  });
  
  // Listen for page errors
  page.on('pageerror', error => {
    console.log(`💥 PAGE ERROR: ${error.message}`);
  });
  
  // Listen for failed requests
  page.on('requestfailed', request => {
    console.log(`❌ FAILED REQUEST: ${request.url()} - ${request.failure().errorText}`);
  });
  
  console.log('🔍 Debugging Home Page...');
  try {
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    
    // Wait a bit for any errors to surface
    await page.waitForTimeout(3000);
    
    // Check what's actually in the page
    const content = await page.content();
    console.log('📄 Page content length:', content.length);
    
    // Look for error messages in the DOM
    const errorElements = await page.$$('.error, [data-error], .nextjs__container');
    console.log(`Found ${errorElements.length} potential error elements`);
    
    for (let i = 0; i < errorElements.length; i++) {
      const errorText = await errorElements[i].textContent();
      console.log(`Error ${i + 1}: ${errorText}`);
    }
    
  } catch (error) {
    console.log(`Debug failed: ${error.message}`);
  }
  
  console.log('\\n🔍 Debugging Evolution Page...');
  try {
    await page.goto('http://localhost:3000/evolution');
    await page.waitForLoadState('networkidle');
    
    // Wait a bit for any errors to surface
    await page.waitForTimeout(3000);
    
    // Check what's actually in the page
    const content = await page.content();
    console.log('📄 Page content length:', content.length);
    
    // Look for any headings that exist
    const headings = await page.$$('h1, h2, h3, h4, h5, h6');
    console.log(`Found ${headings.length} headings`);
    
    for (let i = 0; i < headings.length; i++) {
      const headingText = await headings[i].textContent();
      const tagName = await headings[i].evaluate(el => el.tagName);
      console.log(`Heading ${i + 1} (${tagName}): ${headingText}`);
    }
    
    // Look for error messages in the DOM
    const errorElements = await page.$$('.error, [data-error], .nextjs__container');
    console.log(`Found ${errorElements.length} potential error elements`);
    
    for (let i = 0; i < errorElements.length; i++) {
      const errorText = await errorElements[i].textContent();
      console.log(`Error ${i + 1}: ${errorText}`);
    }
    
  } catch (error) {
    console.log(`Debug failed: ${error.message}`);
  }
  
  // Keep browser open for manual inspection
  console.log('\\n🔍 Browser will stay open for manual inspection. Close it manually when done.');
  console.log('Check the browser console for any JavaScript errors.');
  
  // Don't close the browser automatically
  // await browser.close();
}

debugApplication().catch(console.error);