const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

// Test configuration
const EVOLUTION_PAGE_URL = '/evolution';
const SCREENSHOTS_DIR = 'evolution-test-screenshots';
const REPORT_FILE = 'evolution-comprehensive-report.json';

// Create screenshots directory
if (!fs.existsSync(SCREENSHOTS_DIR)) {
  fs.mkdirSync(SCREENSHOTS_DIR, { recursive: true });
}

// Test report
let testReport = {
  timestamp: new Date().toISOString(),
  pageUrl: EVOLUTION_PAGE_URL,
  testResults: [],
  technicalFindings: {
    pageLoadTime: 0,
    apiCalls: [],
    consoleErrors: [],
    consoleWarnings: [],
    networkRequests: [],
    renderingIssues: []
  },
  recommendations: [],
  summary: {
    status: 'PENDING',
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    issues: []
  }
};

// Helper function to add test result
function addTestResult(name, status, description, details = {}) {
  testReport.testResults.push({
    testName: name,
    status,
    description,
    details,
    timestamp: new Date().toISOString()
  });
  
  testReport.summary.totalTests++;
  if (status === 'PASS') {
    testReport.summary.passedTests++;
  } else if (status === 'FAIL') {
    testReport.summary.failedTests++;
    testReport.summary.issues.push(`${name}: ${description}`);
  }
}

test.describe('Evolution Dashboard Comprehensive Analysis', () => {
  let page;
  let startTime;

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage;
    startTime = Date.now();
    
    // Set up monitoring
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        testReport.technicalFindings.consoleErrors.push({
          type: msg.type(),
          text: msg.text(),
          timestamp: new Date().toISOString()
        });
      } else if (msg.type() === 'warning') {
        testReport.technicalFindings.consoleWarnings.push({
          type: msg.type(),
          text: msg.text(),
          timestamp: new Date().toISOString()
        });
      }
    });

    page.on('request', (request) => {
      testReport.technicalFindings.networkRequests.push({
        method: request.method(),
        url: request.url(),
        timestamp: new Date().toISOString()
      });
      
      if (request.url().includes('/api/v1/evolution')) {
        testReport.technicalFindings.apiCalls.push({
          method: request.method(),
          url: request.url(),
          timestamp: new Date().toISOString()
        });
      }
    });

    page.on('pageerror', (exception) => {
      testReport.technicalFindings.consoleErrors.push({
        type: 'pageerror',
        text: exception.message,
        timestamp: new Date().toISOString()
      });
    });
  });

  test('Complete Evolution Dashboard Analysis', async () => {
    console.log('🔍 Starting comprehensive evolution dashboard analysis...');
    
    try {
      // 1. Navigation Test
      console.log('📍 Testing navigation...');
      await page.goto(EVOLUTION_PAGE_URL, { waitUntil: 'domcontentloaded' });
      testReport.technicalFindings.pageLoadTime = Date.now() - startTime;
      
      // Take initial screenshot
      await page.screenshot({ 
        path: path.join(SCREENSHOTS_DIR, '01-initial-load.png'), 
        fullPage: true 
      });
      
      addTestResult(
        'Page Navigation',
        'PASS',
        'Successfully navigated to evolution dashboard',
        { 
          url: page.url(),
          loadTime: testReport.technicalFindings.pageLoadTime 
        }
      );

      // 2. HTML Structure Analysis
      console.log('🏗️ Analyzing HTML structure...');
      const htmlContent = await page.content();
      const hasMainContent = htmlContent.includes('Evolution Intelligence');
      const hasLoadingState = htmlContent.includes('Loading...');
      const hasErrorState = htmlContent.includes('Error loading');
      
      addTestResult(
        'HTML Structure Analysis',
        hasMainContent ? 'PASS' : 'FAIL',
        `Page content analysis: Main content=${hasMainContent}, Loading=${hasLoadingState}, Error=${hasErrorState}`,
        { 
          hasMainContent,
          hasLoadingState,
          hasErrorState,
          contentLength: htmlContent.length 
        }
      );

      // 3. Component Loading State Analysis
      console.log('🔄 Analyzing component loading...');
      
      // Check for loading indicators
      const loadingElements = await page.locator('text="Loading"').count();
      const loadingDivs = await page.locator('div').filter({ hasText: 'Loading' }).count();
      
      // Check for expected components
      const headingExists = await page.locator('h1').count() > 0;
      const cardElements = await page.locator('[class*="card"]').count();
      
      // Wait for potential content to load
      await page.waitForTimeout(5000);
      
      // Check again after wait
      const loadingAfterWait = await page.locator('text="Loading"').count();
      const cardsAfterWait = await page.locator('[class*="card"]').count();
      
      await page.screenshot({ 
        path: path.join(SCREENSHOTS_DIR, '02-component-analysis.png'), 
        fullPage: true 
      });
      
      const componentStatus = loadingAfterWait === 0 && cardsAfterWait > 0 ? 'PASS' : 'FAIL';
      addTestResult(
        'Component Loading Analysis',
        componentStatus,
        `Component loading analysis: ${loadingAfterWait} loading elements, ${cardsAfterWait} cards found`,
        {
          initialLoading: loadingElements,
          finalLoading: loadingAfterWait,
          initialCards: cardElements,
          finalCards: cardsAfterWait,
          headingExists
        }
      );

      // 4. API Integration Test
      console.log('🔌 Testing API integration...');
      
      // Check if API calls were made
      const evolutionApiCalls = testReport.technicalFindings.apiCalls.filter(call => 
        call.url.includes('/evolution/status')
      );
      
      // Test API directly
      const apiResponse = await page.evaluate(async () => {
        try {
          const response = await fetch('/api/v1/evolution/status?user_id=default-user');
          return {
            status: response.status,
            ok: response.ok,
            data: await response.json()
          };
        } catch (error) {
          return {
            error: error.message
          };
        }
      });
      
      addTestResult(
        'API Integration',
        apiResponse.ok ? 'PASS' : 'FAIL',
        `API integration test: ${evolutionApiCalls.length} API calls made, API response status: ${apiResponse.status}`,
        {
          apiCalls: evolutionApiCalls.length,
          apiResponse
        }
      );

      // 5. UI Element Testing
      console.log('🎨 Testing UI elements...');
      
      // Check for specific UI elements
      const uiElements = {
        navbar: await page.locator('header').count(),
        mainContent: await page.locator('main').count(),
        footer: await page.locator('footer').count(),
        buttons: await page.locator('button').count(),
        cards: await page.locator('[class*="card"]').count(),
        badges: await page.locator('[class*="badge"]').count()
      };
      
      await page.screenshot({ 
        path: path.join(SCREENSHOTS_DIR, '03-ui-elements.png'), 
        fullPage: true 
      });
      
      addTestResult(
        'UI Elements',
        uiElements.navbar > 0 && uiElements.mainContent > 0 ? 'PASS' : 'FAIL',
        `UI elements found: ${Object.entries(uiElements).map(([key, value]) => `${key}=${value}`).join(', ')}`,
        uiElements
      );

      // 6. Responsive Design Test
      console.log('📱 Testing responsive design...');
      
      const viewports = [
        { name: 'Desktop', width: 1920, height: 1080 },
        { name: 'Tablet', width: 768, height: 1024 },
        { name: 'Mobile', width: 375, height: 667 }
      ];
      
      const responsiveResults = [];
      for (const viewport of viewports) {
        await page.setViewportSize(viewport);
        await page.waitForTimeout(1000);
        
        const screenshotPath = path.join(SCREENSHOTS_DIR, `04-responsive-${viewport.name.toLowerCase()}.png`);
        await page.screenshot({ path: screenshotPath, fullPage: true });
        
        const visibleElements = await page.locator('body *').count();
        responsiveResults.push({
          ...viewport,
          visibleElements,
          screenshot: screenshotPath
        });
      }
      
      addTestResult(
        'Responsive Design',
        'PASS',
        `Responsive design tested across ${viewports.length} viewports`,
        { viewports: responsiveResults }
      );

      // 7. Performance Analysis
      console.log('⚡ Analyzing performance...');
      
      const performanceMetrics = await page.evaluate(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        return {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          totalLoadTime: navigation.loadEventEnd - navigation.fetchStart
        };
      });
      
      addTestResult(
        'Performance Metrics',
        performanceMetrics.totalLoadTime < 5000 ? 'PASS' : 'FAIL',
        `Performance analysis: Total load time ${performanceMetrics.totalLoadTime}ms`,
        performanceMetrics
      );

      // 8. Error Handling Test
      console.log('🚨 Testing error handling...');
      
      const errorHandling = {
        consoleErrors: testReport.technicalFindings.consoleErrors.length,
        consoleWarnings: testReport.technicalFindings.consoleWarnings.length,
        networkErrors: testReport.technicalFindings.networkRequests.filter(req => 
          req.url.includes('404') || req.url.includes('500')
        ).length
      };
      
      addTestResult(
        'Error Handling',
        errorHandling.consoleErrors === 0 ? 'PASS' : 'WARNING',
        `Error analysis: ${errorHandling.consoleErrors} console errors, ${errorHandling.consoleWarnings} warnings`,
        errorHandling
      );

      // 9. Final State Analysis
      console.log('🔍 Final state analysis...');
      
      await page.screenshot({ 
        path: path.join(SCREENSHOTS_DIR, '05-final-state.png'), 
        fullPage: true 
      });
      
      // Check if the page is still in loading state
      const finalLoadingState = await page.locator('text="Loading"').count();
      const finalErrorState = await page.locator('text="Error"').count();
      
      if (finalLoadingState > 0) {
        testReport.technicalFindings.renderingIssues.push({
          issue: 'Page stuck in loading state',
          description: 'The evolution dashboard appears to be stuck in a loading state, preventing full functionality',
          recommendation: 'Check useEvolutionStatus hook and API connectivity'
        });
      }
      
      addTestResult(
        'Final State Analysis',
        finalLoadingState === 0 && finalErrorState === 0 ? 'PASS' : 'FAIL',
        `Final state: ${finalLoadingState} loading elements, ${finalErrorState} error elements`,
        {
          finalLoadingState,
          finalErrorState,
          pageStuck: finalLoadingState > 0
        }
      );

      // Generate recommendations
      if (testReport.technicalFindings.renderingIssues.length > 0) {
        testReport.recommendations.push('Fix component loading issues preventing dashboard from rendering');
      }
      
      if (testReport.technicalFindings.consoleErrors.length > 0) {
        testReport.recommendations.push('Address console errors that may affect functionality');
      }
      
      if (testReport.technicalFindings.apiCalls.length === 0) {
        testReport.recommendations.push('Verify API integration and ensure evolution status calls are made');
      }
      
      testReport.summary.status = testReport.summary.failedTests === 0 ? 'PASS' : 'FAIL';

      console.log('✅ Comprehensive analysis completed');

    } catch (error) {
      console.error('❌ Analysis failed:', error.message);
      
      await page.screenshot({ 
        path: path.join(SCREENSHOTS_DIR, '99-error-state.png'), 
        fullPage: true 
      });
      
      addTestResult(
        'Analysis Error',
        'FAIL',
        `Comprehensive analysis failed: ${error.message}`,
        { error: error.message }
      );
      
      testReport.summary.status = 'FAIL';
    }
  });

  test.afterAll(async () => {
    // Save comprehensive report
    fs.writeFileSync(REPORT_FILE, JSON.stringify(testReport, null, 2));
    
    // Generate summary report
    const summaryReport = generateSummaryReport(testReport);
    fs.writeFileSync('evolution-summary-report.txt', summaryReport);
    
    console.log('\n' + '='.repeat(100));
    console.log('🎯 EVOLUTION DASHBOARD COMPREHENSIVE TEST REPORT');
    console.log('='.repeat(100));
    console.log(summaryReport);
    console.log('='.repeat(100));
    console.log(`📊 Detailed Report: ${REPORT_FILE}`);
    console.log(`📸 Screenshots: ${SCREENSHOTS_DIR}/`);
    console.log('='.repeat(100));
  });
});

function generateSummaryReport(report) {
  const summary = [];
  
  summary.push('EVOLUTION DASHBOARD TEST SUMMARY');
  summary.push('='.repeat(50));
  summary.push('');
  summary.push(`🕐 Test Execution: ${report.timestamp}`);
  summary.push(`📍 Page URL: ${report.pageUrl}`);
  summary.push(`⏱️ Page Load Time: ${report.technicalFindings.pageLoadTime}ms`);
  summary.push(`🎯 Overall Status: ${report.summary.status}`);
  summary.push('');
  
  summary.push('TEST RESULTS:');
  summary.push(`📊 Total Tests: ${report.summary.totalTests}`);
  summary.push(`✅ Passed: ${report.summary.passedTests}`);
  summary.push(`❌ Failed: ${report.summary.failedTests}`);
  summary.push('');
  
  summary.push('TECHNICAL FINDINGS:');
  summary.push(`🔗 API Calls: ${report.technicalFindings.apiCalls.length}`);
  summary.push(`🌐 Network Requests: ${report.technicalFindings.networkRequests.length}`);
  summary.push(`❌ Console Errors: ${report.technicalFindings.consoleErrors.length}`);
  summary.push(`⚠️ Console Warnings: ${report.technicalFindings.consoleWarnings.length}`);
  summary.push(`🐛 Rendering Issues: ${report.technicalFindings.renderingIssues.length}`);
  summary.push('');
  
  if (report.technicalFindings.consoleErrors.length > 0) {
    summary.push('CONSOLE ERRORS:');
    report.technicalFindings.consoleErrors.forEach(error => {
      summary.push(`  • ${error.text}`);
    });
    summary.push('');
  }
  
  if (report.technicalFindings.renderingIssues.length > 0) {
    summary.push('RENDERING ISSUES:');
    report.technicalFindings.renderingIssues.forEach(issue => {
      summary.push(`  • ${issue.issue}: ${issue.description}`);
      summary.push(`    Recommendation: ${issue.recommendation}`);
    });
    summary.push('');
  }
  
  if (report.recommendations.length > 0) {
    summary.push('RECOMMENDATIONS:');
    report.recommendations.forEach(rec => {
      summary.push(`  • ${rec}`);
    });
    summary.push('');
  }
  
  summary.push('KEY FINDINGS:');
  report.testResults.forEach(result => {
    const statusIcon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
    summary.push(`  ${statusIcon} ${result.testName}: ${result.description}`);
  });
  
  return summary.join('\n');
}