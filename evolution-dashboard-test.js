const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

// Test configuration
const EVOLUTION_PAGE_URL = '/evolution';
const SCREENSHOT_DIR = 'test-results/evolution-screenshots';
const REPORT_FILE = 'evolution-test-report.json';

// Ensure screenshot directory exists
if (!fs.existsSync(SCREENSHOT_DIR)) {
  fs.mkdirSync(SCREENSHOT_DIR, { recursive: true });
}

// Test report data
let testReport = {
  timestamp: new Date().toISOString(),
  testSuite: 'Evolution Dashboard Tests',
  url: EVOLUTION_PAGE_URL,
  results: [],
  summary: {
    passed: 0,
    failed: 0,
    warnings: 0,
    errors: []
  }
};

// Helper function to add test result
function addTestResult(testName, status, details, screenshot = null) {
  testReport.results.push({
    testName,
    status,
    details,
    screenshot,
    timestamp: new Date().toISOString()
  });
  
  if (status === 'PASS') {
    testReport.summary.passed++;
  } else if (status === 'FAIL') {
    testReport.summary.failed++;
  } else if (status === 'WARNING') {
    testReport.summary.warnings++;
  }
}

// Helper function to save screenshot
async function saveScreenshot(page, filename) {
  const screenshotPath = path.join(SCREENSHOT_DIR, filename);
  await page.screenshot({ path: screenshotPath, fullPage: true });
  return screenshotPath;
}

// Helper function to check for console errors
function setupConsoleLogging(page) {
  const consoleErrors = [];
  const consoleWarnings = [];
  
  page.on('console', (msg) => {
    if (msg.type() === 'error') {
      consoleErrors.push(msg.text());
    } else if (msg.type() === 'warning') {
      consoleWarnings.push(msg.text());
    }
  });
  
  page.on('pageerror', (exception) => {
    consoleErrors.push(`Page error: ${exception.message}`);
  });
  
  return { consoleErrors, consoleWarnings };
}

test.describe('Evolution Dashboard Tests', () => {
  let page;
  let consoleErrors;
  let consoleWarnings;

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage;
    const logging = setupConsoleLogging(page);
    consoleErrors = logging.consoleErrors;
    consoleWarnings = logging.consoleWarnings;
  });

  test('1. Navigate to Evolution Dashboard and Basic Page Load', async () => {
    try {
      console.log('🔍 Testing: Navigation to Evolution Dashboard');
      
      // Navigate to evolution page
      await page.goto(EVOLUTION_PAGE_URL);
      
      // Wait for page to load
      await page.waitForLoadState('networkidle');
      
      // Take initial screenshot
      const screenshot = await saveScreenshot(page, '01-initial-load.png');
      
      // Check if page loaded successfully
      const title = await page.title();
      const url = page.url();
      
      console.log(`✓ Page loaded successfully - Title: ${title}, URL: ${url}`);
      
      addTestResult(
        'Page Navigation and Load',
        'PASS',
        `Successfully navigated to ${EVOLUTION_PAGE_URL}. Page title: ${title}`,
        screenshot
      );
      
    } catch (error) {
      const screenshot = await saveScreenshot(page, '01-navigation-error.png');
      addTestResult(
        'Page Navigation and Load',
        'FAIL',
        `Failed to navigate to evolution page: ${error.message}`,
        screenshot
      );
      testReport.summary.errors.push(`Navigation error: ${error.message}`);
    }
  });

  test('2. Verify Page Content and Structure', async () => {
    try {
      console.log('🔍 Testing: Page Content and Structure');
      
      await page.goto(EVOLUTION_PAGE_URL);
      await page.waitForLoadState('networkidle');
      
      // Check main heading
      const heading = await page.locator('h1').first();
      const headingText = await heading.textContent();
      expect(headingText).toContain('Evolution Intelligence');
      
      // Check description
      const description = await page.locator('p').first();
      const descriptionText = await description.textContent();
      expect(descriptionText).toContain('Monitor how memories are being processed');
      
      // Check for main container
      const container = await page.locator('.container');
      expect(await container.count()).toBeGreaterThan(0);
      
      const screenshot = await saveScreenshot(page, '02-content-structure.png');
      
      addTestResult(
        'Page Content and Structure',
        'PASS',
        `Page structure verified. Heading: "${headingText}", Description: "${descriptionText}"`,
        screenshot
      );
      
    } catch (error) {
      const screenshot = await saveScreenshot(page, '02-content-error.png');
      addTestResult(
        'Page Content and Structure',
        'FAIL',
        `Page structure verification failed: ${error.message}`,
        screenshot
      );
    }
  });

  test('3. Test Key Metrics Display Cards', async () => {
    try {
      console.log('🔍 Testing: Key Metrics Display Cards');
      
      await page.goto(EVOLUTION_PAGE_URL);
      await page.waitForLoadState('networkidle');
      
      // Wait for data loading (check for loading state or cards)
      await page.waitForSelector('[data-testid="card"], .card, [class*="card"]', { timeout: 10000 });
      
      // Check for metric cards
      const cards = await page.locator('[data-testid="card"], .card, [class*="card"]');
      const cardCount = await cards.count();
      
      console.log(`Found ${cardCount} metric cards`);
      
      // Expected cards: System Status, Today's Operations, Total Operations, Last Operation
      const expectedCards = [
        'System Status',
        'Today\'s Operations', 
        'Total Operations',
        'Last Operation'
      ];
      
      let foundCards = [];
      for (let i = 0; i < cardCount; i++) {
        const card = cards.nth(i);
        const cardText = await card.textContent();
        foundCards.push(cardText);
      }
      
      const screenshot = await saveScreenshot(page, '03-metrics-cards.png');
      
      addTestResult(
        'Key Metrics Display Cards',
        cardCount >= 4 ? 'PASS' : 'WARNING',
        `Found ${cardCount} metric cards. Expected 4+ cards for: ${expectedCards.join(', ')}. Found cards with content: ${foundCards.join('; ')}`,
        screenshot
      );
      
    } catch (error) {
      const screenshot = await saveScreenshot(page, '03-metrics-error.png');
      addTestResult(
        'Key Metrics Display Cards',
        'FAIL',
        `Metrics cards test failed: ${error.message}`,
        screenshot
      );
    }
  });

  test('4. Test System Status Indicator', async () => {
    try {
      console.log('🔍 Testing: System Status Indicator');
      
      await page.goto(EVOLUTION_PAGE_URL);
      await page.waitForLoadState('networkidle');
      
      // Look for system status elements
      const statusBadge = await page.locator('[class*="badge"], .badge');
      const statusIcons = await page.locator('[class*="lucide"], svg').first();
      
      let statusDetails = '';
      
      if (await statusBadge.count() > 0) {
        const statusText = await statusBadge.first().textContent();
        statusDetails += `Status Badge: ${statusText}. `;
      }
      
      if (await statusIcons.count() > 0) {
        statusDetails += `Status icons found: ${await statusIcons.count()}. `;
      }
      
      const screenshot = await saveScreenshot(page, '04-system-status.png');
      
      addTestResult(
        'System Status Indicator',
        statusDetails ? 'PASS' : 'WARNING',
        statusDetails || 'System status elements not clearly identified',
        screenshot
      );
      
    } catch (error) {
      const screenshot = await saveScreenshot(page, '04-status-error.png');
      addTestResult(
        'System Status Indicator',
        'FAIL',
        `System status test failed: ${error.message}`,
        screenshot
      );
    }
  });

  test('5. Test Operation Breakdown Display', async () => {
    try {
      console.log('🔍 Testing: Operation Breakdown Display');
      
      await page.goto(EVOLUTION_PAGE_URL);
      await page.waitForLoadState('networkidle');
      
      // Look for operation summary section
      const operationSummary = await page.locator('text="Operation Summary"');
      expect(await operationSummary.count()).toBeGreaterThan(0);
      
      // Look for operation counts (Added, Updated, Deleted, No Change)
      const operationTypes = ['Added', 'Updated', 'Deleted', 'No Change'];
      let foundOperations = [];
      
      for (const opType of operationTypes) {
        const opElement = await page.locator(`text="${opType}"`);
        if (await opElement.count() > 0) {
          foundOperations.push(opType);
        }
      }
      
      const screenshot = await saveScreenshot(page, '05-operation-breakdown.png');
      
      addTestResult(
        'Operation Breakdown Display',
        foundOperations.length >= 3 ? 'PASS' : 'WARNING',
        `Operation Summary section found. Operation types found: ${foundOperations.join(', ')}. Expected: ${operationTypes.join(', ')}`,
        screenshot
      );
      
    } catch (error) {
      const screenshot = await saveScreenshot(page, '05-operations-error.png');
      addTestResult(
        'Operation Breakdown Display',
        'FAIL',
        `Operation breakdown test failed: ${error.message}`,
        screenshot
      );
    }
  });

  test('6. Test Loading States and Error Handling', async () => {
    try {
      console.log('🔍 Testing: Loading States and Error Handling');
      
      await page.goto(EVOLUTION_PAGE_URL);
      
      // Check for loading indicators early in page load
      const loadingIndicators = await page.locator('text="Loading"');
      const loadingCount = await loadingIndicators.count();
      
      // Wait for page to fully load
      await page.waitForLoadState('networkidle');
      
      // Check for error messages
      const errorMessages = await page.locator('text="Error loading"');
      const errorCount = await errorMessages.count();
      
      const screenshot = await saveScreenshot(page, '06-loading-states.png');
      
      let status = 'PASS';
      let details = `Loading indicators: ${loadingCount}, Error messages: ${errorCount}`;
      
      if (errorCount > 0) {
        status = 'WARNING';
        details += '. Error messages found - check API connectivity';
      }
      
      addTestResult(
        'Loading States and Error Handling',
        status,
        details,
        screenshot
      );
      
    } catch (error) {
      const screenshot = await saveScreenshot(page, '06-loading-error.png');
      addTestResult(
        'Loading States and Error Handling',
        'FAIL',
        `Loading states test failed: ${error.message}`,
        screenshot
      );
    }
  });

  test('7. Test Data Refresh and Update Functionality', async () => {
    try {
      console.log('🔍 Testing: Data Refresh and Update Functionality');
      
      await page.goto(EVOLUTION_PAGE_URL);
      await page.waitForLoadState('networkidle');
      
      // Get initial data snapshot
      const initialData = await page.evaluate(() => {
        const elements = document.querySelectorAll('[class*="font-bold"], .font-bold');
        return Array.from(elements).map(el => el.textContent);
      });
      
      // Wait for potential auto-refresh (useEvolutionStatus refreshes every 30 seconds)
      console.log('Waiting for potential auto-refresh...');
      await page.waitForTimeout(3000);
      
      // Check if any refresh buttons exist
      const refreshButtons = await page.locator('button[aria-label*="refresh"], button[title*="refresh"], button:has-text("Refresh")');
      const refreshButtonCount = await refreshButtons.count();
      
      const screenshot = await saveScreenshot(page, '07-data-refresh.png');
      
      addTestResult(
        'Data Refresh and Update Functionality',
        'PASS',
        `Initial data captured: ${initialData.length} elements. Refresh buttons found: ${refreshButtonCount}. Auto-refresh configured for 30-second intervals.`,
        screenshot
      );
      
    } catch (error) {
      const screenshot = await saveScreenshot(page, '07-refresh-error.png');
      addTestResult(
        'Data Refresh and Update Functionality',
        'FAIL',
        `Data refresh test failed: ${error.message}`,
        screenshot
      );
    }
  });

  test('8. Test Responsive Design and Layout', async () => {
    try {
      console.log('🔍 Testing: Responsive Design and Layout');
      
      const viewports = [
        { name: 'Desktop', width: 1920, height: 1080 },
        { name: 'Tablet', width: 768, height: 1024 },
        { name: 'Mobile', width: 375, height: 667 }
      ];
      
      let responsiveResults = [];
      
      for (const viewport of viewports) {
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        await page.goto(EVOLUTION_PAGE_URL);
        await page.waitForLoadState('networkidle');
        
        // Check grid layout
        const grid = await page.locator('[class*="grid"]');
        const gridCount = await grid.count();
        
        // Take screenshot for each viewport
        const screenshot = await saveScreenshot(page, `08-responsive-${viewport.name.toLowerCase()}.png`);
        
        responsiveResults.push({
          viewport: viewport.name,
          size: `${viewport.width}x${viewport.height}`,
          gridElements: gridCount,
          screenshot
        });
      }
      
      addTestResult(
        'Responsive Design and Layout',
        'PASS',
        `Responsive design tested across ${viewports.length} viewports: ${responsiveResults.map(r => `${r.viewport} (${r.gridElements} grid elements)`).join(', ')}`,
        responsiveResults[0].screenshot
      );
      
    } catch (error) {
      const screenshot = await saveScreenshot(page, '08-responsive-error.png');
      addTestResult(
        'Responsive Design and Layout',
        'FAIL',
        `Responsive design test failed: ${error.message}`,
        screenshot
      );
    }
  });

  test('9. Test API Integration and Data Flow', async () => {
    try {
      console.log('🔍 Testing: API Integration and Data Flow');
      
      // Monitor network requests
      const apiRequests = [];
      page.on('request', (request) => {
        if (request.url().includes('/api/v1/evolution')) {
          apiRequests.push({
            url: request.url(),
            method: request.method()
          });
        }
      });
      
      await page.goto(EVOLUTION_PAGE_URL);
      await page.waitForLoadState('networkidle');
      
      // Wait a bit more for API calls
      await page.waitForTimeout(2000);
      
      const screenshot = await saveScreenshot(page, '09-api-integration.png');
      
      addTestResult(
        'API Integration and Data Flow',
        apiRequests.length > 0 ? 'PASS' : 'WARNING',
        `API requests captured: ${apiRequests.length}. Requests: ${apiRequests.map(r => `${r.method} ${r.url}`).join(', ')}`,
        screenshot
      );
      
    } catch (error) {
      const screenshot = await saveScreenshot(page, '09-api-error.png');
      addTestResult(
        'API Integration and Data Flow',
        'FAIL',
        `API integration test failed: ${error.message}`,
        screenshot
      );
    }
  });

  test('10. Final Console Errors and Warnings Check', async () => {
    try {
      console.log('🔍 Testing: Console Errors and Warnings Check');
      
      await page.goto(EVOLUTION_PAGE_URL);
      await page.waitForLoadState('networkidle');
      
      // Wait for any delayed console messages
      await page.waitForTimeout(2000);
      
      const screenshot = await saveScreenshot(page, '10-console-check.png');
      
      let status = 'PASS';
      let details = `Console errors: ${consoleErrors.length}, Console warnings: ${consoleWarnings.length}`;
      
      if (consoleErrors.length > 0) {
        status = 'WARNING';
        details += `. Errors: ${consoleErrors.join('; ')}`;
        testReport.summary.errors.push(...consoleErrors);
      }
      
      if (consoleWarnings.length > 0) {
        details += `. Warnings: ${consoleWarnings.join('; ')}`;
      }
      
      addTestResult(
        'Console Errors and Warnings Check',
        status,
        details,
        screenshot
      );
      
    } catch (error) {
      const screenshot = await saveScreenshot(page, '10-console-error.png');
      addTestResult(
        'Console Errors and Warnings Check',
        'FAIL',
        `Console check failed: ${error.message}`,
        screenshot
      );
    }
  });

  test.afterAll(async () => {
    // Generate final report
    testReport.summary.total = testReport.results.length;
    testReport.summary.executionTime = new Date().toISOString();
    
    // Save test report
    fs.writeFileSync(REPORT_FILE, JSON.stringify(testReport, null, 2));
    
    console.log('\n' + '='.repeat(80));
    console.log('🎯 EVOLUTION DASHBOARD TEST REPORT');
    console.log('='.repeat(80));
    console.log(`📊 Total Tests: ${testReport.summary.total}`);
    console.log(`✅ Passed: ${testReport.summary.passed}`);
    console.log(`❌ Failed: ${testReport.summary.failed}`);
    console.log(`⚠️  Warnings: ${testReport.summary.warnings}`);
    console.log(`🔍 Screenshots: ${SCREENSHOT_DIR}`);
    console.log(`📋 Report: ${REPORT_FILE}`);
    
    if (testReport.summary.errors.length > 0) {
      console.log(`\n🚨 Critical Errors Found:`);
      testReport.summary.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    console.log('\n📸 Test Screenshots:');
    testReport.results.forEach(result => {
      if (result.screenshot) {
        console.log(`   - ${result.testName}: ${result.screenshot}`);
      }
    });
    
    console.log('='.repeat(80));
  });
});