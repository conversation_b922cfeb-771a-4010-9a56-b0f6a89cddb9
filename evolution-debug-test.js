const { test, expect } = require('@playwright/test');

test.describe('Evolution Page Debug', () => {
  test('Debug evolution page loading', async ({ page }) => {
    // Enable console logging
    page.on('console', (msg) => {
      console.log(`🔍 Console ${msg.type()}: ${msg.text()}`);
    });

    // Monitor requests
    page.on('request', (request) => {
      console.log(`📡 Request: ${request.method()} ${request.url()}`);
    });

    // Monitor responses
    page.on('response', (response) => {
      console.log(`📨 Response: ${response.status()} ${response.url()}`);
    });

    // Monitor page errors
    page.on('pageerror', (exception) => {
      console.log(`❌ Page Error: ${exception.message}`);
    });

    console.log('🚀 Starting evolution page debug...');

    try {
      // Navigate to evolution page
      await page.goto('/evolution', { waitUntil: 'domcontentloaded' });
      console.log('✅ Navigation completed');

      // Wait for initial content
      await page.waitForSelector('h1', { timeout: 5000 });
      console.log('✅ H1 found');

      // Check page content
      const heading = await page.locator('h1').textContent();
      console.log(`🎯 Heading: "${heading}"`);

      // Check for loading state
      const loadingElements = await page.locator('text="Loading"').count();
      console.log(`⏳ Loading elements: ${loadingElements}`);

      // Check for error state
      const errorElements = await page.locator('text="Error"').count();
      console.log(`❌ Error elements: ${errorElements}`);

      // Check for cards
      const cardElements = await page.locator('[class*="card"]').count();
      console.log(`🎴 Card elements: ${cardElements}`);

      // Wait for potential data loading
      console.log('⏳ Waiting for data to load...');
      await page.waitForTimeout(5000);

      // Check again for loading state
      const loadingAfterWait = await page.locator('text="Loading"').count();
      console.log(`⏳ Loading elements after wait: ${loadingAfterWait}`);

      // Take screenshot
      await page.screenshot({ path: 'evolution-debug.png', fullPage: true });
      console.log('📸 Screenshot saved: evolution-debug.png');

      // Get page content for analysis
      const pageContent = await page.content();
      console.log(`📄 Page content length: ${pageContent.length} characters`);

      // Check specific elements
      const systemStatusCard = await page.locator('text="System Status"').count();
      console.log(`📊 System Status card: ${systemStatusCard}`);

      const todayOpsCard = await page.locator('text="Today\'s Operations"').count();
      console.log(`📊 Today's Operations card: ${todayOpsCard}`);

      // Check for any badge elements
      const badges = await page.locator('[class*="badge"]').count();
      console.log(`🏷️ Badge elements: ${badges}`);

      console.log('✅ Debug completed successfully');

    } catch (error) {
      console.error('❌ Debug failed:', error.message);
      await page.screenshot({ path: 'evolution-debug-error.png', fullPage: true });
      throw error;
    }
  });
});