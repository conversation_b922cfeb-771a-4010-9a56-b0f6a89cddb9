const { chromium } = require('playwright');
const fs = require('fs');

(async () => {
  console.log('🚀 Starting final Evolution Dashboard evaluation...');
  
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    // Navigate to evolution page
    console.log('📍 Navigating to evolution page...');
    await page.goto('http://localhost:3000/evolution');
    
    // Wait for page to load
    console.log('⏳ Waiting for page to load...');
    await page.waitForLoadState('networkidle');
    
    // Wait additional time for React components
    await page.waitForTimeout(3000);
    
    // Take screenshot
    console.log('📸 Taking screenshot...');
    await page.screenshot({ path: 'evolution-final-screenshot.png', fullPage: true });
    
    // Get page content
    const content = await page.content();
    console.log(`📄 Page content length: ${content.length}`);
    
    // Check for specific elements
    const heading = await page.locator('h1').textContent().catch(() => null);
    console.log(`🎯 Main heading: ${heading || 'Not found'}`);
    
    const loadingElements = await page.locator('text="Loading"').count();
    console.log(`⏳ Loading elements: ${loadingElements}`);
    
    const cardElements = await page.locator('[class*="card"]').count();
    console.log(`🎴 Card elements: ${cardElements}`);
    
    // Check if page is stuck in loading
    const isStuckLoading = content.includes('Loading...') && !content.includes('Evolution Intelligence');
    console.log(`🔄 Stuck in loading: ${isStuckLoading}`);
    
    // Test API call
    console.log('🔌 Testing API call...');
    const apiResponse = await page.evaluate(async () => {
      try {
        const response = await fetch('http://localhost:8765/api/v1/evolution/status?user_id=default-user');
        return await response.json();
      } catch (error) {
        return { error: error.message };
      }
    });
    
    console.log('📊 API Response:', JSON.stringify(apiResponse, null, 2));
    
    console.log('✅ Evaluation completed successfully');
    
  } catch (error) {
    console.error('❌ Error during evaluation:', error);
  } finally {
    await browser.close();
  }
})();