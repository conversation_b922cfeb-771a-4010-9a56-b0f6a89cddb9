const { test, expect } = require('@playwright/test');

test.describe('Simple Evolution Page Test', () => {
  test('Basic navigation to evolution page', async ({ page }) => {
    console.log('🔍 Starting basic evolution page test...');
    
    try {
      // Navigate to evolution page
      console.log('📍 Navigating to /evolution...');
      await page.goto('/evolution');
      
      // Wait for initial load
      console.log('⏳ Waiting for page load...');
      await page.waitForLoadState('domcontentloaded');
      
      // Take screenshot
      await page.screenshot({ path: 'evolution-basic-test.png', fullPage: true });
      console.log('📸 Screenshot saved: evolution-basic-test.png');
      
      // Check page title
      const title = await page.title();
      console.log(`📋 Page title: ${title}`);
      
      // Check for main heading
      const heading = await page.locator('h1').first();
      const headingText = await heading.textContent();
      console.log(`🎯 Main heading: ${headingText}`);
      
      // Check for loading or error states
      const loading = await page.locator('text="Loading"');
      const loadingCount = await loading.count();
      console.log(`⏳ Loading indicators: ${loadingCount}`);
      
      const error = await page.locator('text="Error"');
      const errorCount = await error.count();
      console.log(`❌ Error messages: ${errorCount}`);
      
      // Check for cards
      const cards = await page.locator('[class*="card"]');
      const cardCount = await cards.count();
      console.log(`🎴 Cards found: ${cardCount}`);
      
      // Wait a bit more to see if content loads
      await page.waitForTimeout(3000);
      
      // Take final screenshot
      await page.screenshot({ path: 'evolution-final-test.png', fullPage: true });
      console.log('📸 Final screenshot saved: evolution-final-test.png');
      
      console.log('✅ Basic test completed successfully');
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
      await page.screenshot({ path: 'evolution-error-test.png', fullPage: true });
      throw error;
    }
  });
});