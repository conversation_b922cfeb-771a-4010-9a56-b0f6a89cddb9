{"name": "memory-master-settings-test", "version": "1.0.0", "description": "Playwright test for Memory Master settings pages", "main": "test-settings-playwright.js", "scripts": {"test": "node test-settings-playwright.js", "install-playwright": "npx playwright install", "install-deps": "npm install playwright"}, "dependencies": {"playwright": "^1.40.0"}, "devDependencies": {}, "keywords": ["playwright", "testing", "automation"], "author": "Memory Master Team", "license": "MIT"}