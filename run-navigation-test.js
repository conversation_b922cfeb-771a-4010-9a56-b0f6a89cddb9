const { chromium } = require('playwright');

async function runNavigationTest() {
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  const results = {
    testResults: [],
    errors: [],
    consoleErrors: [],
    accessibilityIssues: []
  };

  // Monitor console errors
  page.on('console', (msg) => {
    if (msg.type() === 'error') {
      results.consoleErrors.push(msg.text());
    }
  });

  // Monitor network errors
  page.on('response', (response) => {
    if (response.status() >= 400) {
      results.errors.push(`Network error: ${response.status()} ${response.url()}`);
    }
  });

  try {
    console.log('🔍 Testing Memory Master Navigation and UI Components...\n');

    // Test 1: Homepage Load
    console.log('1. Testing homepage load...');
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    
    const header = await page.locator('header').isVisible();
    const title = await page.locator('text=MemoryMaster').isVisible();
    
    results.testResults.push({
      test: 'Homepage Load',
      passed: header && title,
      details: `Header visible: ${header}, Title visible: ${title}`
    });
    
    // Test 2: Navigation Menu
    console.log('2. Testing navigation menu...');
    const navTests = [
      { name: 'Dashboard', href: '/', expectedUrl: 'http://localhost:3000/' },
      { name: 'Memories', href: '/memories', expectedUrl: 'http://localhost:3000/memories' },
      { name: 'Evolution', href: '/evolution', expectedUrl: 'http://localhost:3000/evolution' },
      { name: 'Settings', href: '/settings', expectedUrl: 'http://localhost:3000/settings' }
    ];
    
    for (const nav of navTests) {
      try {
        await page.click(`a[href="${nav.href}"]`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(500);
        
        const currentUrl = page.url();
        const navigationWorked = currentUrl === nav.expectedUrl || currentUrl.includes(nav.href);
        
        results.testResults.push({
          test: `Navigation to ${nav.name}`,
          passed: navigationWorked,
          details: `Expected: ${nav.expectedUrl}, Got: ${currentUrl}`
        });
        
        console.log(`   ✓ ${nav.name}: ${navigationWorked ? 'PASS' : 'FAIL'}`);
      } catch (error) {
        results.testResults.push({
          test: `Navigation to ${nav.name}`,
          passed: false,
          details: `Error: ${error.message}`
        });
        console.log(`   ✗ ${nav.name}: FAIL - ${error.message}`);
      }
    }

    // Test 3: Active States
    console.log('3. Testing active states...');
    await page.goto('http://localhost:3000/memories');
    await page.waitForLoadState('networkidle');
    
    const activeButton = page.locator('a[href="/memories"] button');
    const hasActiveClass = await activeButton.evaluate(el => el.className.includes('bg-zinc-800'));
    
    results.testResults.push({
      test: 'Active State Highlighting',
      passed: hasActiveClass,
      details: `Memories button has active class: ${hasActiveClass}`
    });
    
    console.log(`   ✓ Active states: ${hasActiveClass ? 'PASS' : 'FAIL'}`);

    // Test 4: Theme Toggle
    console.log('4. Testing theme toggle...');
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    
    const themeToggle = page.locator('button:has(svg)').last();
    const isThemeToggleVisible = await themeToggle.isVisible();
    
    if (isThemeToggleVisible) {
      await themeToggle.click();
      await page.waitForTimeout(500);
    }
    
    results.testResults.push({
      test: 'Theme Toggle',
      passed: isThemeToggleVisible,
      details: `Theme toggle visible: ${isThemeToggleVisible}`
    });
    
    console.log(`   ✓ Theme toggle: ${isThemeToggleVisible ? 'PASS' : 'FAIL'}`);

    // Test 5: Responsive Design
    console.log('5. Testing responsive design...');
    const viewports = [
      { width: 375, height: 667, name: 'Mobile' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 1920, height: 1080, name: 'Desktop' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      const headerVisible = await page.locator('header').isVisible();
      const navVisible = await page.locator('a[href="/memories"]').isVisible();
      
      results.testResults.push({
        test: `Responsive Design - ${viewport.name}`,
        passed: headerVisible && navVisible,
        details: `Header: ${headerVisible}, Navigation: ${navVisible}`
      });
      
      console.log(`   ✓ ${viewport.name}: ${headerVisible && navVisible ? 'PASS' : 'FAIL'}`);
    }

    // Test 6: 404 Page
    console.log('6. Testing 404 page...');
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.goto('http://localhost:3000/nonexistent-page');
    await page.waitForLoadState('networkidle');
    
    const notFoundText = await page.locator('text=404').isVisible();
    const homeLink = await page.locator('a[href="/"]').isVisible();
    
    results.testResults.push({
      test: '404 Page',
      passed: notFoundText,
      details: `404 text visible: ${notFoundText}, Home link visible: ${homeLink}`
    });
    
    console.log(`   ✓ 404 page: ${notFoundText ? 'PASS' : 'FAIL'}`);

    // Test 7: Accessibility Check
    console.log('7. Testing accessibility...');
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    
    // Check for images without alt text
    const images = await page.locator('img').all();
    let missingAltCount = 0;
    
    for (const img of images) {
      const alt = await img.getAttribute('alt');
      if (!alt || alt.trim() === '') {
        const src = await img.getAttribute('src');
        results.accessibilityIssues.push(`Missing alt text on image: ${src}`);
        missingAltCount++;
      }
    }
    
    // Check for interactive elements without labels
    const buttons = await page.locator('button').all();
    let unlabeledButtonCount = 0;
    
    for (const button of buttons) {
      const ariaLabel = await button.getAttribute('aria-label');
      const text = await button.textContent();
      
      if ((!ariaLabel || ariaLabel.trim() === '') && (!text || text.trim() === '')) {
        results.accessibilityIssues.push('Button without label or text');
        unlabeledButtonCount++;
      }
    }
    
    const accessibilityPassed = missingAltCount === 0 && unlabeledButtonCount === 0;
    
    results.testResults.push({
      test: 'Accessibility Check',
      passed: accessibilityPassed,
      details: `Missing alt text: ${missingAltCount}, Unlabeled buttons: ${unlabeledButtonCount}`
    });
    
    console.log(`   ✓ Accessibility: ${accessibilityPassed ? 'PASS' : 'FAIL'}`);

    // Test 8: Performance Check
    console.log('8. Testing performance...');
    const performanceTests = [
      { page: '/', name: 'Homepage' },
      { page: '/memories', name: 'Memories' },
      { page: '/evolution', name: 'Evolution' },
      { page: '/settings', name: 'Settings' }
    ];
    
    for (const perfTest of performanceTests) {
      const startTime = Date.now();
      await page.goto(`http://localhost:3000${perfTest.page}`);
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;
      
      const performanceGood = loadTime < 5000; // 5 seconds
      
      results.testResults.push({
        test: `Performance - ${perfTest.name}`,
        passed: performanceGood,
        details: `Load time: ${loadTime}ms`
      });
      
      console.log(`   ✓ ${perfTest.name}: ${performanceGood ? 'PASS' : 'FAIL'} (${loadTime}ms)`);
    }

  } catch (error) {
    console.error('Test error:', error);
    results.errors.push(`Test execution error: ${error.message}`);
  }

  await browser.close();
  
  // Generate Report
  console.log('\n📊 TEST RESULTS SUMMARY');
  console.log('=' .repeat(50));
  
  const passedTests = results.testResults.filter(t => t.passed).length;
  const totalTests = results.testResults.length;
  
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${totalTests - passedTests}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  console.log('\n📋 DETAILED RESULTS:');
  results.testResults.forEach((result, index) => {
    console.log(`${index + 1}. ${result.test}: ${result.passed ? '✅ PASS' : '❌ FAIL'}`);
    if (result.details) {
      console.log(`   Details: ${result.details}`);
    }
  });
  
  if (results.consoleErrors.length > 0) {
    console.log('\n🚨 CONSOLE ERRORS:');
    results.consoleErrors.forEach(error => console.log(`   - ${error}`));
  }
  
  if (results.errors.length > 0) {
    console.log('\n🚨 NETWORK/EXECUTION ERRORS:');
    results.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  if (results.accessibilityIssues.length > 0) {
    console.log('\n♿ ACCESSIBILITY ISSUES:');
    results.accessibilityIssues.forEach(issue => console.log(`   - ${issue}`));
  }
  
  console.log('\n🎯 RECOMMENDATIONS:');
  
  const failedTests = results.testResults.filter(t => !t.passed);
  if (failedTests.length > 0) {
    console.log('\n❌ Failed Tests to Address:');
    failedTests.forEach(test => {
      console.log(`   - ${test.test}: ${test.details}`);
    });
  }
  
  if (results.accessibilityIssues.length > 0) {
    console.log('\n♿ Accessibility Improvements:');
    console.log('   - Add alt text to all images');
    console.log('   - Ensure all interactive elements have labels');
    console.log('   - Test with screen readers');
  }
  
  if (results.consoleErrors.length > 0) {
    console.log('\n🐛 JavaScript Issues:');
    console.log('   - Fix console errors to improve stability');
    console.log('   - Check for missing dependencies or typos');
  }
  
  console.log('\n✅ Additional Testing Recommendations:');
  console.log('   - Test with keyboard navigation (Tab key)');
  console.log('   - Test with different screen readers');
  console.log('   - Test form validation and error states');
  console.log('   - Test loading states and skeleton screens');
  console.log('   - Test with slow network conditions');
  console.log('   - Test cross-browser compatibility');
  console.log('   - Test with different user preferences (high contrast, reduced motion)');
  
  // Save results to file
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTests,
      passedTests,
      failedTests: totalTests - passedTests,
      successRate: ((passedTests / totalTests) * 100).toFixed(1) + '%'
    },
    testResults: results.testResults,
    consoleErrors: results.consoleErrors,
    errors: results.errors,
    accessibilityIssues: results.accessibilityIssues
  };
  
  require('fs').writeFileSync('navigation-ui-test-report.json', JSON.stringify(reportData, null, 2));
  console.log('\n📄 Detailed report saved to: navigation-ui-test-report.json');
  
  return results;
}

// Run the test
runNavigationTest().catch(console.error);