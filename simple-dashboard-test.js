const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

// Create test results directory
const resultsDir = './test-results';
if (!fs.existsSync(resultsDir)) {
  fs.mkdirSync(resultsDir, { recursive: true });
}

test.describe('Dashboard Page Tests', () => {
  let consoleMessages = [];
  let errors = [];

  test.beforeEach(async ({ page }) => {
    // Clear previous test data
    consoleMessages = [];
    errors = [];

    // Listen for console messages
    page.on('console', msg => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toISOString()
      });
    });

    // Listen for page errors
    page.on('pageerror', err => {
      errors.push({
        message: err.message,
        stack: err.stack,
        timestamp: new Date().toISOString()
      });
    });

    // Set longer timeout for page loads
    page.setDefaultTimeout(10000);
  });

  test('Dashboard page basic test', async ({ page }) => {
    console.log('Starting dashboard basic test...');
    
    try {
      // 1. Navigate to dashboard
      console.log('1. Navigating to http://localhost:3000...');
      await page.goto('http://localhost:3000', { waitUntil: 'domcontentloaded', timeout: 15000 });
      
      console.log('✓ Page loaded successfully');

      // 2. Verify page loads correctly
      console.log('2. Verifying page title...');
      const title = await page.title();
      console.log(`Page title: ${title}`);

      // 3. Take initial screenshot
      console.log('3. Taking initial screenshot...');
      await page.screenshot({ 
        path: path.join(resultsDir, '01-initial-load.png'),
        fullPage: false,
        timeout: 5000
      });
      console.log('✓ Initial screenshot taken');

      // 4. Check for navigation menu
      console.log('4. Checking navigation menu...');
      const navbar = await page.locator('nav, [role="navigation"], .navbar, header').first();
      const navbarVisible = await navbar.isVisible();
      console.log(`Navigation visible: ${navbarVisible}`);

      // 5. Check for main elements
      console.log('5. Checking for main page elements...');
      
      // Check for MemoryMaster title/logo
      const logo = await page.locator('img[alt*="Memory"], span:has-text("MemoryMaster")').first();
      const logoVisible = await logo.isVisible().catch(() => false);
      console.log(`Logo/title visible: ${logoVisible}`);

      // Check for Create Memory button
      const createBtn = await page.locator('button:has-text("Create Memory")').first();
      const createBtnVisible = await createBtn.isVisible().catch(() => false);
      console.log(`Create Memory button visible: ${createBtnVisible}`);

      // Check for theme toggle
      const themeBtn = await page.locator('button[class*="theme"], button:has([class*="sun"]), button:has([class*="moon"])').first();
      const themeBtnVisible = await themeBtn.isVisible().catch(() => false);
      console.log(`Theme toggle visible: ${themeBtnVisible}`);

      // Check for refresh button
      const refreshBtn = await page.locator('button:has-text("Refresh")').first();
      const refreshBtnVisible = await refreshBtn.isVisible().catch(() => false);
      console.log(`Refresh button visible: ${refreshBtnVisible}`);

      // 6. Take final screenshot
      console.log('6. Taking final screenshot...');
      await page.screenshot({ 
        path: path.join(resultsDir, '02-final-state.png'),
        fullPage: false,
        timeout: 5000
      });

      console.log('Dashboard basic test completed successfully!');

    } catch (error) {
      console.error('Test failed:', error.message);
      
      try {
        await page.screenshot({ 
          path: path.join(resultsDir, '99-error-state.png'),
          fullPage: false,
          timeout: 3000
        });
      } catch (screenshotError) {
        console.error('Failed to take error screenshot:', screenshotError.message);
      }
      
      throw error;
    }
  });

  test('Navigation functionality test', async ({ page }) => {
    console.log('Starting navigation functionality test...');
    
    try {
      await page.goto('http://localhost:3000', { waitUntil: 'domcontentloaded', timeout: 15000 });
      
      // Test navigation links
      const navLinks = await page.locator('nav a, header a').filter({ hasText: /memories|dashboard|settings|evolution/i });
      const navCount = await navLinks.count();
      console.log(`Found ${navCount} navigation links`);

      for (let i = 0; i < Math.min(navCount, 3); i++) {
        const link = navLinks.nth(i);
        const linkText = await link.textContent();
        console.log(`Testing navigation link: ${linkText}`);
        
        if (linkText && !linkText.toLowerCase().includes('logout')) {
          await link.click();
          await page.waitForTimeout(1000);
          console.log(`✓ Clicked ${linkText} link successfully`);
        }
      }

      console.log('Navigation functionality test completed!');

    } catch (error) {
      console.error('Navigation test failed:', error.message);
      throw error;
    }
  });

  test('Interactive elements test', async ({ page }) => {
    console.log('Starting interactive elements test...');
    
    try {
      await page.goto('http://localhost:3000', { waitUntil: 'domcontentloaded', timeout: 15000 });
      
      // Test Create Memory button
      console.log('Testing Create Memory button...');
      try {
        const createMemoryButton = await page.locator('button:has-text("Create Memory")').first();
        
        if (await createMemoryButton.isVisible()) {
          await createMemoryButton.click();
          await page.waitForTimeout(1000);
          console.log('✓ Create Memory button clicked successfully');
          
          // Try to close modal if it opened
          const closeButton = await page.locator('button:has-text("Cancel"), button:has-text("Close"), [aria-label="Close"]').first();
          if (await closeButton.isVisible().catch(() => false)) {
            await closeButton.click();
            console.log('✓ Modal closed successfully');
          } else {
            await page.keyboard.press('Escape');
            console.log('✓ Modal closed with Escape key');
          }
        } else {
          console.log('⚠ Create Memory button not visible');
        }
      } catch (e) {
        console.log('⚠ Create Memory button test failed:', e.message);
      }

      // Test theme toggle
      console.log('Testing theme toggle...');
      try {
        const themeToggle = await page.locator('button[class*="theme"], button:has([class*="sun"]), button:has([class*="moon"])').first();

        if (await themeToggle.isVisible()) {
          await themeToggle.click();
          await page.waitForTimeout(500);
          console.log('✓ Theme toggle clicked successfully');
        } else {
          console.log('⚠ Theme toggle button not visible');
        }
      } catch (e) {
        console.log('⚠ Theme toggle test failed:', e.message);
      }

      // Test refresh button
      console.log('Testing refresh functionality...');
      try {
        const refreshButton = await page.locator('button:has-text("Refresh")').first();

        if (await refreshButton.isVisible()) {
          await refreshButton.click();
          await page.waitForTimeout(1000);
          console.log('✓ Refresh button clicked successfully');
        } else {
          console.log('⚠ Refresh button not visible');
        }
      } catch (e) {
        console.log('⚠ Refresh button test failed:', e.message);
      }

      console.log('Interactive elements test completed!');

    } catch (error) {
      console.error('Interactive elements test failed:', error.message);
      throw error;
    }
  });

  test.afterEach(async () => {
    // Generate test report
    const reportData = {
      timestamp: new Date().toISOString(),
      consoleMessages: consoleMessages,
      errors: errors,
      testSummary: {
        totalConsoleMessages: consoleMessages.length,
        errorCount: errors.length,
        warningCount: consoleMessages.filter(msg => msg.type === 'warning').length,
        infoCount: consoleMessages.filter(msg => msg.type === 'info').length,
        logCount: consoleMessages.filter(msg => msg.type === 'log').length
      }
    };

    fs.writeFileSync(
      path.join(resultsDir, `test-report-${Date.now()}.json`), 
      JSON.stringify(reportData, null, 2)
    );

    console.log(`Test completed. Console messages: ${reportData.testSummary.totalConsoleMessages}, Errors: ${reportData.testSummary.errorCount}`);
  });
});