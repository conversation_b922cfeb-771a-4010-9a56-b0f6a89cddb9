# Simplified Configuration Implementation

## Overview
This implementation adds simplified configuration endpoints to the existing Memory Master v2 backend while maintaining full compatibility with the existing complex configuration system.

## Changes Made

### 1. Added SimpleConfig Model
```python
class SimpleConfig(BaseModel):
    openaiApiKey: str = ""
    userId: str = "tradelink"
    customPrompt: str = ""
    systemEnabled: bool = True
```

### 2. New API Endpoints

#### GET /api/v1/config/simple
- Returns simplified configuration suitable for 2-person team
- Extracts essential settings from existing complex configuration
- Masks API keys for security (shows "***" if configured)
- Maintains backward compatibility with existing database structure

#### POST /api/v1/config/simple
- Updates simplified configuration
- Stores API key references as environment variables
- Updates custom prompt in the openmemory configuration
- Triggers memory client reset to apply changes
- Maintains all existing configuration while updating only essential fields

#### POST /api/v1/config/reset
- Resets configuration to default values
- Uses existing default configuration system
- Maintains compatibility with existing database schema
- Triggers memory client reset

## Implementation Details

### Key Features
1. **Backward Compatibility**: All existing endpoints continue to work unchanged
2. **Security**: API keys are masked in responses and stored as environment variable references
3. **Automatic Migration**: Existing configurations are automatically extracted and simplified
4. **Error Handling**: Comprehensive error handling with detailed logging
5. **Database Integration**: Uses existing configuration database structure

### Configuration Mapping
- `openaiApiKey` → `mem0.llm.config.api_key` and `mem0.embedder.config.api_key`
- `userId` → Environment variable `USER` (default: "tradelink")
- `customPrompt` → `openmemory.custom_instructions`
- `systemEnabled` → Always `true` (simplified interface)

### API Key Handling
- Input: Direct API key from frontend
- Storage: Stored as `"env:OPENAI_API_KEY"` reference
- Security: Masked as `"***"` in responses when configured
- Applied to both LLM and embedder configurations

## Usage Examples

### Get Current Configuration
```bash
curl -X GET "http://localhost:8765/api/v1/config/simple"
```

### Update Configuration
```bash
curl -X POST "http://localhost:8765/api/v1/config/simple" \
  -H "Content-Type: application/json" \
  -d '{
    "openaiApiKey": "sk-your-key-here",
    "userId": "tradelink",
    "customPrompt": "Custom instructions for dropshipping business",
    "systemEnabled": true
  }'
```

### Reset to Defaults
```bash
curl -X POST "http://localhost:8765/api/v1/config/reset"
```

## Testing

A test script has been created at `/test_simple_config.py` to verify the endpoints work correctly:

```bash
python3 test_simple_config.py
```

## Frontend Integration

The simplified endpoints are designed to work with the simplified settings page as specified in the Phase 5 PRD:

- Frontend makes requests to `/api/v1/config/simple`
- Configuration is automatically extracted from complex backend structure
- Changes are persisted using existing database infrastructure
- All existing complex configurations remain intact

## Benefits

1. **Simplified Interface**: Reduces complexity from 594+ lines to essential fields
2. **Maintained Power**: All existing functionality remains available
3. **Security**: Proper API key handling with masking
4. **Compatibility**: No breaking changes to existing system
5. **Performance**: Efficient extraction from existing configuration structure

## Migration Path

No migration is required. The simplified endpoints work with existing configurations:
- Existing configurations are automatically recognized and simplified
- New configurations are stored in the same database structure
- Complex configurations can still be accessed via existing endpoints
- System maintains full backward compatibility

This implementation provides the foundation for Phase 5 UI simplification while preserving all existing functionality and data.