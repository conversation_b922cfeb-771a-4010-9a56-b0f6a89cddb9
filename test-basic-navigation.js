const { chromium } = require('playwright');

async function basicTest() {
  console.log('🔍 Testing Memory Master Basic Navigation...\n');
  
  const browser = await chromium.launch({ headless: true });
  const page = await browser.newPage();
  
  const results = [];
  const errors = [];
  
  try {
    // Test 1: Homepage
    console.log('1. Testing homepage...');
    await page.goto('http://localhost:3000', { timeout: 10000 });
    await page.waitForTimeout(2000);
    
    const header = await page.locator('header').isVisible();
    const title = await page.locator('text=MemoryMaster').isVisible();
    
    results.push({
      test: 'Homepage Load',
      passed: header && title,
      details: `Header: ${header}, Title: ${title}`
    });
    
    console.log(`   Header visible: ${header}`);
    console.log(`   Title visible: ${title}`);
    
    // Test 2: Navigation Links
    console.log('\n2. Testing navigation links...');
    const navLinks = [
      { name: 'Dashboard', href: '/' },
      { name: 'Memories', href: '/memories' },
      { name: 'Evolution', href: '/evolution' },
      { name: '<PERSON>ting<PERSON>', href: '/settings' }
    ];
    
    for (const nav of navLinks) {
      try {
        const link = await page.locator(`a[href="${nav.href}"]`).isVisible();
        console.log(`   ${nav.name} link visible: ${link}`);
        
        results.push({
          test: `${nav.name} Link Visible`,
          passed: link,
          details: `Link visibility: ${link}`
        });
        
        if (link) {
          await page.click(`a[href="${nav.href}"]`);
          await page.waitForTimeout(1000);
          const url = page.url();
          console.log(`   Clicked ${nav.name}, URL: ${url}`);
        }
      } catch (error) {
        console.log(`   Error testing ${nav.name}: ${error.message}`);
        errors.push(`${nav.name}: ${error.message}`);
      }
    }
    
    // Test 3: UI Components
    console.log('\n3. Testing UI components...');
    await page.goto('http://localhost:3000');
    await page.waitForTimeout(2000);
    
    const themeToggle = await page.locator('button:has(svg)').last().isVisible();
    const createMemoryButton = await page.locator('button:has-text("Create Memory")').isVisible();
    const refreshButton = await page.locator('button:has-text("Refresh")').isVisible();
    
    console.log(`   Theme toggle visible: ${themeToggle}`);
    console.log(`   Create Memory button visible: ${createMemoryButton}`);
    console.log(`   Refresh button visible: ${refreshButton}`);
    
    results.push({
      test: 'UI Components',
      passed: themeToggle && createMemoryButton && refreshButton,
      details: `Theme: ${themeToggle}, Create: ${createMemoryButton}, Refresh: ${refreshButton}`
    });
    
    // Test 4: Responsive Design
    console.log('\n4. Testing responsive design...');
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForTimeout(1000);
    
    const mobileHeader = await page.locator('header').isVisible();
    const mobileNav = await page.locator('a[href="/memories"]').isVisible();
    
    console.log(`   Mobile header visible: ${mobileHeader}`);
    console.log(`   Mobile navigation visible: ${mobileNav}`);
    
    results.push({
      test: 'Mobile Responsive',
      passed: mobileHeader && mobileNav,
      details: `Header: ${mobileHeader}, Nav: ${mobileNav}`
    });
    
    // Test 5: 404 Page
    console.log('\n5. Testing 404 page...');
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.goto('http://localhost:3000/nonexistent-page');
    await page.waitForTimeout(2000);
    
    const notFound = await page.locator('text=404').isVisible();
    console.log(`   404 page visible: ${notFound}`);
    
    results.push({
      test: '404 Page',
      passed: notFound,
      details: `404 text visible: ${notFound}`
    });
    
  } catch (error) {
    console.error('Test error:', error);
    errors.push(`Test execution error: ${error.message}`);
  }
  
  await browser.close();
  
  // Results
  console.log('\n📊 TEST RESULTS:');
  console.log('='.repeat(40));
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  console.log(`Total Tests: ${total}`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${total - passed}`);
  console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
  
  console.log('\nDetailed Results:');
  results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.test}: ${result.passed ? '✅ PASS' : '❌ FAIL'}`);
    if (result.details) {
      console.log(`   ${result.details}`);
    }
  });
  
  if (errors.length > 0) {
    console.log('\n🚨 ERRORS:');
    errors.forEach(error => console.log(`   - ${error}`));
  }
  
  console.log('\n✅ Test completed!');
  
  return { results, errors };
}

basicTest().catch(console.error);