const { test, expect } = require('@playwright/test');

test('debug navigation test', async ({ page }) => {
  console.log('Starting test...');
  
  try {
    await page.goto('http://localhost:3000');
    console.log('Navigated to homepage');
    
    await page.waitForTimeout(2000);
    console.log('Waited 2 seconds');
    
    const header = page.locator('header');
    const isHeaderVisible = await header.isVisible();
    console.log('Header visible:', isHeaderVisible);
    
    if (isHeaderVisible) {
      console.log('Header found, taking screenshot');
      await page.screenshot({ path: 'debug-homepage.png' });
      
      // Try to click memories link
      const memoriesLink = page.locator('a[href="/memories"]');
      const isMemoriesVisible = await memoriesLink.isVisible();
      console.log('Memories link visible:', isMemoriesVisible);
      
      if (isMemoriesVisible) {
        await memoriesLink.click();
        console.log('Clicked memories link');
        await page.waitForTimeout(2000);
        console.log('URL after click:', page.url());
        await page.screenshot({ path: 'debug-memories.png' });
      }
    }
    
    console.log('Test completed successfully');
  } catch (error) {
    console.error('Test error:', error);
    await page.screenshot({ path: 'debug-error.png' });
    throw error;
  }
});