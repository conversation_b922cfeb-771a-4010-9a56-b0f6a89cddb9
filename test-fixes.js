const { chromium } = require('playwright');

async function testFixes() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  console.log('🧪 Testing Memory Filtering and Stats Fixes...\n');
  
  // Test home page stats
  console.log('📊 Testing Home Page Stats...');
  try {
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    
    // Wait for stats to load
    await page.waitForTimeout(3000);
    
    // Check for stats values
    const memoryStats = await page.$$('text=/\\d+.*Memories?/i');
    const appStats = await page.$$('text=/\\d+.*Apps?/i');
    
    if (memoryStats.length > 0) {
      const memoryText = await memoryStats[0].textContent();
      console.log(`✅ Memory stats found: ${memoryText}`);
    } else {
      console.log('❌ Memory stats not found');
    }
    
    if (appStats.length > 0) {
      const appText = await appStats[0].textContent();
      console.log(`✅ App stats found: ${appText}`);
    } else {
      console.log('❌ App stats not found');
    }
    
  } catch (error) {
    console.log(`❌ Home page stats test failed: ${error.message}`);
  }
  
  // Test memories page filtering
  console.log('\\n🧠 Testing Memories Page Filtering...');
  try {
    await page.goto('http://localhost:3000/memories');
    await page.waitForLoadState('networkidle');
    
    // Wait for memories to load
    await page.waitForTimeout(5000);
    
    // Check for memory entries
    const memoryRows = await page.$$('tr, .memory-item, [data-memory]');
    console.log(`Found ${memoryRows.length} memory rows/items`);
    
    // Check for pagination or total count
    const paginationText = await page.$$('text=/\\d+.*of.*\\d+/i, text=/Page.*\\d+/i');
    if (paginationText.length > 0) {
      const paginationInfo = await paginationText[0].textContent();
      console.log(`✅ Pagination info: ${paginationInfo}`);
    }
    
    // Check for "tradelink" specific content or user indicator
    const tradeLinkIndicators = await page.$$('text=tradelink');
    console.log(`Found ${tradeLinkIndicators.length} tradelink indicators`);
    
  } catch (error) {
    console.log(`❌ Memories page test failed: ${error.message}`);
  }
  
  // Test API directly from browser
  console.log('\\n🔗 Testing API Endpoints from Browser...');
  try {
    // Test stats API
    const statsResponse = await page.goto('http://localhost:8765/api/v1/stats?user_id=tradelink');
    const statsData = await statsResponse.json();
    console.log(`✅ Stats API: ${statsData.total_memories} memories, ${statsData.total_apps} apps`);
    
    // Test memory filter API
    const filterResponse = await page.goto('http://localhost:8765/api/v1/memories/filter?user_id=tradelink&page=1&limit=5');
    const filterData = await filterResponse.json();
    console.log(`✅ Filter API: Total ${filterData.total} memories for tradelink user`);
    
  } catch (error) {
    console.log(`❌ API test failed: ${error.message}`);
  }
  
  console.log('\\n🎯 Test Summary:');
  console.log('================');
  console.log('1. Stats should show 1,895 memories and 7+ apps (not 0)');
  console.log('2. Memories page should show tradelink user memories only');
  console.log('3. API endpoints should return tradelink-specific data');
  
  // Keep browser open for manual inspection
  console.log('\\n🔍 Browser will stay open for manual inspection.');
  console.log('Check:');
  console.log('- Home page shows correct memory/app counts');
  console.log('- Memories page shows user-specific memories');
  console.log('- No references to other users visible');
  
  // Don't close automatically
  // await browser.close();
}

testFixes().catch(console.error);