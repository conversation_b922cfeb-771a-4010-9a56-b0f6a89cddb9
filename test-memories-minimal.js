const { chromium } = require('playwright');

async function testMemoriesPage() {
  console.log('🚀 Testing memories page functionality...');
  
  const browser = await chromium.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage', '--disable-gpu']
  });
  
  const page = await browser.newPage();
  
  const results = {
    navigation: false,
    pageLoad: false,
    createButton: false,
    tableElements: false,
    searchElements: false,
    errors: []
  };
  
  try {
    // Navigate to the memories page
    console.log('📄 Navigating to memories page...');
    await page.goto('http://localhost:3000/memories', { 
      waitUntil: 'domcontentloaded',
      timeout: 20000
    });
    
    results.navigation = true;
    console.log('✅ Navigation successful');
    
    // Wait for page to load
    await page.waitForTimeout(3000);
    
    // Check if page loaded properly
    const title = await page.title();
    console.log(`📄 Page title: "${title}"`);
    
    if (title.includes('MemoryMaster')) {
      results.pageLoad = true;
      console.log('✅ Page loaded successfully');
    }
    
    // Test Create Memory button
    console.log('🆕 Testing Create Memory button...');
    const createButtons = await page.locator('button').filter({ hasText: /create.*memory/i }).all();
    
    if (createButtons.length > 0) {
      results.createButton = true;
      console.log(`✅ Found ${createButtons.length} create memory button(s)`);
      
      // Try to click the create button
      const createButton = createButtons[0];
      const buttonText = await createButton.textContent();
      console.log(`📝 Create button text: "${buttonText}"`);
      
      try {
        await createButton.click();
        await page.waitForTimeout(2000);
        
        // Check if modal/dialog opened
        const dialogs = await page.locator('[role="dialog"], .modal, [data-testid="modal"]').count();
        if (dialogs > 0) {
          console.log('✅ Create memory dialog opened');
        }
        
        // Close dialog if it opened
        await page.keyboard.press('Escape');
        await page.waitForTimeout(1000);
        
      } catch (clickError) {
        console.log(`⚠️ Could not click create button: ${clickError.message}`);
      }
    } else {
      console.log('❌ No create memory button found');
    }
    
    // Test table/grid elements
    console.log('📊 Testing table/grid elements...');
    const tables = await page.locator('table').count();
    const gridElements = await page.locator('[data-testid="memories-grid"], .grid').count();
    
    if (tables > 0) {
      results.tableElements = true;
      console.log(`✅ Found ${tables} table(s)`);
      
      // Count table rows
      const rows = await page.locator('tbody tr').count();
      console.log(`📋 Found ${rows} table rows`);
      
    } else if (gridElements > 0) {
      results.tableElements = true;
      console.log(`✅ Found ${gridElements} grid element(s)`);
    } else {
      console.log('❌ No table or grid elements found');
    }
    
    // Test search elements
    console.log('🔍 Testing search elements...');
    const searchInputs = await page.locator('input[type="text"], input[type="search"]').count();
    const filterButtons = await page.locator('button').filter({ hasText: /filter/i }).count();
    
    if (searchInputs > 0 || filterButtons > 0) {
      results.searchElements = true;
      console.log(`✅ Found ${searchInputs} search inputs and ${filterButtons} filter buttons`);
    } else {
      console.log('❌ No search elements found');
    }
    
    // Test pagination
    console.log('📄 Testing pagination...');
    const paginationButtons = await page.locator('button').filter({ hasText: /^(next|prev|previous|\d+)$/i }).count();
    
    if (paginationButtons > 0) {
      console.log(`✅ Found ${paginationButtons} pagination buttons`);
    } else {
      console.log('❌ No pagination buttons found');
    }
    
    // Test memory actions
    console.log('⚙️ Testing memory actions...');
    const actionButtons = await page.locator('button').filter({ hasText: /edit|delete|view|actions/i }).count();
    const moreButtons = await page.locator('button').filter({ hasText: /⋮|⋯|more/i }).count();
    
    if (actionButtons > 0 || moreButtons > 0) {
      console.log(`✅ Found ${actionButtons} action buttons and ${moreButtons} more buttons`);
    } else {
      console.log('❌ No action buttons found');
    }
    
    // Get page content for analysis
    console.log('📋 Getting page content for analysis...');
    const bodyText = await page.locator('body').textContent();
    
    // Check for common UI elements
    const hasMemoriesTitle = bodyText.toLowerCase().includes('memories');
    const hasCreateButton = bodyText.toLowerCase().includes('create');
    const hasSearchText = bodyText.toLowerCase().includes('search') || bodyText.toLowerCase().includes('filter');
    
    console.log(`📄 Page contains 'memories': ${hasMemoriesTitle}`);
    console.log(`🆕 Page contains 'create': ${hasCreateButton}`);
    console.log(`🔍 Page contains search/filter: ${hasSearchText}`);
    
    // Check for loading states
    const loadingElements = await page.locator('[data-testid="loading"], .loading, .spinner').count();
    console.log(`⏳ Found ${loadingElements} loading elements`);
    
    // Check for error states
    const errorElements = await page.locator('[data-testid="error"], .error, .alert-destructive').count();
    console.log(`❌ Found ${errorElements} error elements`);
    
    // Count total elements
    const totalButtons = await page.locator('button').count();
    const totalInputs = await page.locator('input').count();
    const totalLinks = await page.locator('a').count();
    
    console.log(`🔘 Total buttons: ${totalButtons}`);
    console.log(`📝 Total inputs: ${totalInputs}`);
    console.log(`🔗 Total links: ${totalLinks}`);
    
    // Take a minimal screenshot (smaller viewport)
    console.log('📸 Attempting minimal screenshot...');
    try {
      await page.setViewportSize({ width: 1200, height: 800 });
      await page.screenshot({ 
        path: '/tmp/memories-page-minimal.png',
        fullPage: false,
        timeout: 5000
      });
      console.log('✅ Minimal screenshot saved to /tmp/memories-page-minimal.png');
    } catch (screenshotError) {
      console.log(`⚠️ Screenshot failed: ${screenshotError.message}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    results.errors.push(error.message);
  } finally {
    await browser.close();
  }
  
  return results;
}

testMemoriesPage().then(results => {
  console.log('\n🎯 TEST RESULTS SUMMARY:');
  console.log('========================');
  console.log(`📄 Navigation: ${results.navigation ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`📋 Page Load: ${results.pageLoad ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`🆕 Create Button: ${results.createButton ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`📊 Table Elements: ${results.tableElements ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`🔍 Search Elements: ${results.searchElements ? '✅ PASS' : '❌ FAIL'}`);
  
  if (results.errors.length > 0) {
    console.log(`\n❌ Errors encountered:`);
    results.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  console.log('\n✅ Test completed!');
}).catch(error => {
  console.error('❌ Test runner failed:', error.message);
});