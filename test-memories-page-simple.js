const { chromium } = require('playwright');

async function testMemoriesPage() {
  console.log('🚀 Starting memories page test...');
  
  const browser = await chromium.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 }
  });
  
  const page = await context.newPage();
  
  const testResults = {
    pageLoad: { success: false, errors: [] },
    screenshot: { success: false, path: '' },
    createMemory: { success: false, errors: [] },
    formValidation: { success: false, errors: [] },
    tableDisplay: { success: false, errors: [] },
    filtering: { success: false, errors: [] },
    pagination: { success: false, errors: [] },
    memoryActions: { success: false, errors: [] },
    consoleErrors: [],
    jsErrors: [],
    pageContent: ''
  };

  // Listen for console messages and errors
  page.on('console', msg => {
    if (msg.type() === 'error') {
      testResults.consoleErrors.push(msg.text());
    }
  });

  page.on('pageerror', error => {
    testResults.jsErrors.push(error.message);
  });

  try {
    // 1. Navigate to memories page
    console.log('📄 Navigating to memories page...');
    await page.goto('http://localhost:3000/memories', { 
      waitUntil: 'networkidle',
      timeout: 30000
    });
    
    // Wait for page to load
    await page.waitForTimeout(3000);
    
    testResults.pageLoad.success = true;
    console.log('✅ Page loaded successfully');

    // Get page content for analysis
    testResults.pageContent = await page.content();
    console.log('📋 Page content captured');

    // 2. Take screenshot with shorter timeout
    console.log('📸 Taking screenshot...');
    try {
      const screenshotPath = '/tmp/memories-page-screenshot.png';
      await page.screenshot({ 
        path: screenshotPath, 
        fullPage: false,
        timeout: 10000
      });
      testResults.screenshot.success = true;
      testResults.screenshot.path = screenshotPath;
      console.log(`✅ Screenshot saved to: ${screenshotPath}`);
    } catch (screenshotError) {
      testResults.screenshot.errors = [`Screenshot failed: ${screenshotError.message}`];
      console.log('❌ Screenshot failed, continuing with other tests...');
    }

    // 3. Test page structure
    console.log('🔍 Testing page structure...');
    
    const pageTitle = await page.title();
    console.log(`📄 Page title: ${pageTitle}`);
    
    // Check for main elements
    const mainHeading = await page.locator('h1, h2').first();
    if (await mainHeading.isVisible()) {
      const headingText = await mainHeading.textContent();
      console.log(`✅ Main heading found: "${headingText}"`);
    }

    // 4. Test Create Memory functionality
    console.log('🆕 Testing Create Memory functionality...');
    
    try {
      // Look for Create Memory button with various text patterns
      const createButtons = await page.locator('button').filter({ hasText: /create/i }).all();
      console.log(`🔍 Found ${createButtons.length} buttons with 'create' text`);
      
      if (createButtons.length > 0) {
        const createButton = createButtons[0];
        const buttonText = await createButton.textContent();
        console.log(`📝 Found create button: "${buttonText}"`);
        
        await createButton.click();
        await page.waitForTimeout(2000);
        
        // Look for form or dialog
        const dialogs = await page.locator('[role="dialog"], .modal, [data-testid="modal"]').all();
        const forms = await page.locator('form').all();
        
        if (dialogs.length > 0) {
          console.log('✅ Dialog/Modal opened');
          testResults.createMemory.success = true;
          
          // Try to close dialog
          await page.keyboard.press('Escape');
          await page.waitForTimeout(1000);
        } else if (forms.length > 0) {
          console.log('✅ Form found');
          testResults.createMemory.success = true;
        }
      } else {
        testResults.createMemory.errors.push('No create buttons found');
      }
    } catch (error) {
      testResults.createMemory.errors.push(`Create memory test failed: ${error.message}`);
    }

    // 5. Test memory table/list display
    console.log('📊 Testing memory display...');
    
    try {
      // Look for table
      const tables = await page.locator('table').all();
      if (tables.length > 0) {
        console.log(`✅ Found ${tables.length} table(s)`);
        
        const rows = await page.locator('tbody tr').all();
        console.log(`📋 Found ${rows.length} table rows`);
        
        testResults.tableDisplay.success = true;
      } else {
        // Look for card/grid layout
        const cards = await page.locator('.card, [data-testid="memory-card"]').all();
        if (cards.length > 0) {
          console.log(`✅ Found ${cards.length} memory cards`);
          testResults.tableDisplay.success = true;
        } else {
          testResults.tableDisplay.errors.push('No table or card layout found');
        }
      }
    } catch (error) {
      testResults.tableDisplay.errors.push(`Table display test failed: ${error.message}`);
    }

    // 6. Test search/filter functionality
    console.log('🔍 Testing search functionality...');
    
    try {
      const searchInputs = await page.locator('input[type="text"]').all();
      const searchPlaceholders = [];
      
      for (const input of searchInputs) {
        const placeholder = await input.getAttribute('placeholder');
        if (placeholder) {
          searchPlaceholders.push(placeholder);
        }
      }
      
      console.log(`📝 Found ${searchInputs.length} text inputs with placeholders: ${searchPlaceholders.join(', ')}`);
      
      if (searchInputs.length > 0) {
        testResults.filtering.success = true;
        console.log('✅ Search inputs found');
      } else {
        testResults.filtering.errors.push('No search inputs found');
      }
    } catch (error) {
      testResults.filtering.errors.push(`Search test failed: ${error.message}`);
    }

    // 7. Test pagination
    console.log('📄 Testing pagination...');
    
    try {
      const paginationElements = await page.locator('nav, .pagination').all();
      const pageButtons = await page.locator('button').filter({ hasText: /^\d+$|next|prev|previous/i }).all();
      
      if (paginationElements.length > 0 || pageButtons.length > 0) {
        console.log(`✅ Found pagination elements: ${paginationElements.length} nav elements, ${pageButtons.length} page buttons`);
        testResults.pagination.success = true;
      } else {
        testResults.pagination.errors.push('No pagination found');
      }
    } catch (error) {
      testResults.pagination.errors.push(`Pagination test failed: ${error.message}`);
    }

    // 8. Test memory actions
    console.log('⚙️ Testing memory actions...');
    
    try {
      const actionButtons = await page.locator('button').filter({ hasText: /edit|delete|view|actions/i }).all();
      const moreButtons = await page.locator('button').filter({ hasText: /⋮|⋯|more/i }).all();
      
      console.log(`🔍 Found ${actionButtons.length} action buttons and ${moreButtons.length} more buttons`);
      
      if (actionButtons.length > 0 || moreButtons.length > 0) {
        testResults.memoryActions.success = true;
        console.log('✅ Action buttons found');
      } else {
        testResults.memoryActions.errors.push('No action buttons found');
      }
    } catch (error) {
      testResults.memoryActions.errors.push(`Memory actions test failed: ${error.message}`);
    }

    // Wait for any async operations
    await page.waitForTimeout(2000);

  } catch (error) {
    console.error('❌ Test failed:', error);
    testResults.pageLoad.errors.push(`Test failed: ${error.message}`);
  } finally {
    await browser.close();
  }

  return testResults;
}

// Run the test
testMemoriesPage().then(results => {
  console.log('\n🎯 TEST RESULTS SUMMARY:');
  console.log('========================');
  
  console.log(`📄 Page Load: ${results.pageLoad.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.pageLoad.errors.length > 0) {
    console.log(`   Errors: ${results.pageLoad.errors.join(', ')}`);
  }
  
  console.log(`📸 Screenshot: ${results.screenshot.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.screenshot.success && results.screenshot.path) {
    console.log(`   Screenshot: ${results.screenshot.path}`);
  }
  
  console.log(`🆕 Create Memory: ${results.createMemory.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.createMemory.errors.length > 0) {
    console.log(`   Errors: ${results.createMemory.errors.join(', ')}`);
  }
  
  console.log(`📋 Form Validation: ${results.formValidation.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.formValidation.errors.length > 0) {
    console.log(`   Errors: ${results.formValidation.errors.join(', ')}`);
  }
  
  console.log(`📊 Table Display: ${results.tableDisplay.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.tableDisplay.errors.length > 0) {
    console.log(`   Errors: ${results.tableDisplay.errors.join(', ')}`);
  }
  
  console.log(`🔍 Filtering: ${results.filtering.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.filtering.errors.length > 0) {
    console.log(`   Errors: ${results.filtering.errors.join(', ')}`);
  }
  
  console.log(`📄 Pagination: ${results.pagination.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.pagination.errors.length > 0) {
    console.log(`   Errors: ${results.pagination.errors.join(', ')}`);
  }
  
  console.log(`⚙️ Memory Actions: ${results.memoryActions.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.memoryActions.errors.length > 0) {
    console.log(`   Errors: ${results.memoryActions.errors.join(', ')}`);
  }
  
  if (results.consoleErrors.length > 0) {
    console.log(`🚨 Console Errors (${results.consoleErrors.length}):`);
    results.consoleErrors.forEach(error => console.log(`   - ${error}`));
  }
  
  if (results.jsErrors.length > 0) {
    console.log(`🚨 JavaScript Errors (${results.jsErrors.length}):`);
    results.jsErrors.forEach(error => console.log(`   - ${error}`));
  }
  
  console.log('\n✅ Test completed!');
  
  // Save results to file
  const fs = require('fs');
  fs.writeFileSync('/tmp/memories-test-results.json', JSON.stringify(results, null, 2));
  console.log('📄 Detailed results saved to: /tmp/memories-test-results.json');
  
}).catch(error => {
  console.error('❌ Test runner failed:', error);
});