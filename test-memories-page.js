const { chromium } = require('playwright');

async function testMemoriesPage() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 }
  });
  const page = await context.newPage();
  
  const testResults = {
    pageLoad: { success: false, errors: [] },
    screenshot: { success: false, path: '' },
    createMemory: { success: false, errors: [] },
    formValidation: { success: false, errors: [] },
    tableDisplay: { success: false, errors: [] },
    filtering: { success: false, errors: [] },
    pagination: { success: false, errors: [] },
    memoryActions: { success: false, errors: [] },
    consoleErrors: [],
    jsErrors: []
  };

  // Listen for console messages and errors
  page.on('console', msg => {
    if (msg.type() === 'error') {
      testResults.consoleErrors.push(msg.text());
    }
  });

  page.on('pageerror', error => {
    testResults.jsErrors.push(error.message);
  });

  try {
    console.log('🚀 Starting memories page test...');
    
    // 1. Navigate to memories page
    console.log('📄 Navigating to memories page...');
    await page.goto('http://localhost:3000/memories', { waitUntil: 'networkidle' });
    
    // Wait for page to load
    await page.waitForTimeout(2000);
    
    testResults.pageLoad.success = true;
    console.log('✅ Page loaded successfully');

    // 2. Take screenshot
    console.log('📸 Taking screenshot...');
    const screenshotPath = '/tmp/memories-page-screenshot.png';
    await page.screenshot({ path: screenshotPath, fullPage: true });
    testResults.screenshot.success = true;
    testResults.screenshot.path = screenshotPath;
    console.log(`✅ Screenshot saved to: ${screenshotPath}`);

    // 3. Test page elements and layout
    console.log('🔍 Testing page elements...');
    
    // Check for main page elements
    const pageTitle = await page.locator('h1, h2, [data-testid="page-title"]').first();
    const createButton = await page.locator('button').filter({ hasText: /create/i }).first();
    const tableOrGrid = await page.locator('table, [data-testid="memories-table"], [data-testid="memories-grid"]').first();
    
    if (await pageTitle.isVisible()) {
      console.log('✅ Page title found');
    } else {
      testResults.tableDisplay.errors.push('Page title not found');
    }

    // 4. Test Create Memory functionality
    console.log('🆕 Testing Create Memory functionality...');
    
    try {
      // Look for Create Memory button
      const createMemoryButton = await page.locator('button').filter({ hasText: /create.*memory/i }).first();
      
      if (await createMemoryButton.isVisible()) {
        console.log('✅ Create Memory button found');
        await createMemoryButton.click();
        
        // Wait for dialog/modal to appear
        await page.waitForTimeout(1000);
        
        // Look for form fields
        const formFields = await page.locator('input, textarea, select').all();
        console.log(`📝 Found ${formFields.length} form fields`);
        
        if (formFields.length > 0) {
          // Try to fill form fields
          const textInputs = await page.locator('input[type="text"], textarea').all();
          
          for (let i = 0; i < textInputs.length; i++) {
            const input = textInputs[i];
            const placeholder = await input.getAttribute('placeholder');
            const name = await input.getAttribute('name');
            
            if (placeholder?.toLowerCase().includes('title') || name?.toLowerCase().includes('title')) {
              await input.fill('Test Memory Title');
              console.log('✅ Filled title field');
            } else if (placeholder?.toLowerCase().includes('content') || name?.toLowerCase().includes('content')) {
              await input.fill('This is a test memory content for automation testing');
              console.log('✅ Filled content field');
            } else if (placeholder?.toLowerCase().includes('category') || name?.toLowerCase().includes('category')) {
              await input.fill('Test Category');
              console.log('✅ Filled category field');
            } else {
              await input.fill('Test Value');
              console.log(`✅ Filled field: ${placeholder || name || 'unknown'}`);
            }
          }
          
          // Test form validation by submitting empty form first
          console.log('🔍 Testing form validation...');
          await page.locator('input, textarea').first().fill('');
          
          const submitButton = await page.locator('button').filter({ hasText: /submit|create|save/i }).first();
          if (await submitButton.isVisible()) {
            await submitButton.click();
            await page.waitForTimeout(1000);
            
            // Check for validation errors
            const validationErrors = await page.locator('.error, [data-testid="error"], .text-red-500').all();
            if (validationErrors.length > 0) {
              console.log('✅ Form validation working');
              testResults.formValidation.success = true;
            }
          }
          
          // Fill form properly and try to submit
          await textInputs[0].fill('Test Memory Title');
          if (textInputs.length > 1) {
            await textInputs[1].fill('This is a test memory content for automation testing');
          }
          
          if (await submitButton.isVisible()) {
            await submitButton.click();
            await page.waitForTimeout(2000);
            testResults.createMemory.success = true;
            console.log('✅ Create memory form submitted');
          }
        }
        
        // Close dialog/modal
        const closeButton = await page.locator('button').filter({ hasText: /close|cancel|×/i }).first();
        if (await closeButton.isVisible()) {
          await closeButton.click();
        } else {
          await page.keyboard.press('Escape');
        }
        
      } else {
        testResults.createMemory.errors.push('Create Memory button not found');
      }
    } catch (error) {
      testResults.createMemory.errors.push(`Create memory test failed: ${error.message}`);
    }

    // 5. Test memory table/list display
    console.log('📊 Testing memory table/list display...');
    
    try {
      // Look for table or grid
      const memoryTable = await page.locator('table, [data-testid="memories-table"]').first();
      const memoryGrid = await page.locator('[data-testid="memories-grid"], .grid, .memory-grid').first();
      
      if (await memoryTable.isVisible()) {
        console.log('✅ Memory table found');
        
        // Check table headers
        const headers = await page.locator('th, [data-testid="table-header"]').all();
        console.log(`📋 Found ${headers.length} table headers`);
        
        // Check table rows
        const rows = await page.locator('tbody tr, [data-testid="memory-row"]').all();
        console.log(`📋 Found ${rows.length} memory rows`);
        
        testResults.tableDisplay.success = true;
        
      } else if (await memoryGrid.isVisible()) {
        console.log('✅ Memory grid found');
        
        const gridItems = await page.locator('.memory-card, [data-testid="memory-card"]').all();
        console.log(`📋 Found ${gridItems.length} memory items`);
        
        testResults.tableDisplay.success = true;
      } else {
        testResults.tableDisplay.errors.push('No memory table or grid found');
      }
    } catch (error) {
      testResults.tableDisplay.errors.push(`Table display test failed: ${error.message}`);
    }

    // 6. Test filtering and search functionality
    console.log('🔍 Testing filtering and search functionality...');
    
    try {
      // Look for search input
      const searchInput = await page.locator('input[placeholder*="search" i], input[placeholder*="filter" i], [data-testid="search-input"]').first();
      
      if (await searchInput.isVisible()) {
        console.log('✅ Search input found');
        await searchInput.fill('test');
        await page.waitForTimeout(1000);
        
        // Press Enter or look for search button
        await searchInput.press('Enter');
        await page.waitForTimeout(1500);
        
        console.log('✅ Search functionality tested');
        testResults.filtering.success = true;
      }
      
      // Look for filter buttons/dropdowns
      const filterButtons = await page.locator('button').filter({ hasText: /filter/i }).all();
      const filterDropdowns = await page.locator('select').all();
      
      if (filterButtons.length > 0) {
        console.log(`✅ Found ${filterButtons.length} filter buttons`);
        // Test first filter button
        await filterButtons[0].click();
        await page.waitForTimeout(1000);
      }
      
      if (filterDropdowns.length > 0) {
        console.log(`✅ Found ${filterDropdowns.length} filter dropdowns`);
      }
      
    } catch (error) {
      testResults.filtering.errors.push(`Filtering test failed: ${error.message}`);
    }

    // 7. Test pagination
    console.log('📄 Testing pagination...');
    
    try {
      const paginationContainer = await page.locator('.pagination, [data-testid="pagination"]').first();
      const nextButton = await page.locator('button').filter({ hasText: /next|>/i }).first();
      const prevButton = await page.locator('button').filter({ hasText: /prev|previous|</i }).first();
      const pageNumbers = await page.locator('button').filter({ hasText: /^\d+$/ }).all();
      
      if (await paginationContainer.isVisible() || await nextButton.isVisible() || pageNumbers.length > 0) {
        console.log('✅ Pagination found');
        
        if (await nextButton.isVisible()) {
          await nextButton.click();
          await page.waitForTimeout(1500);
          console.log('✅ Next page navigation tested');
        }
        
        if (await prevButton.isVisible()) {
          await prevButton.click();
          await page.waitForTimeout(1500);
          console.log('✅ Previous page navigation tested');
        }
        
        testResults.pagination.success = true;
      } else {
        testResults.pagination.errors.push('No pagination found');
      }
    } catch (error) {
      testResults.pagination.errors.push(`Pagination test failed: ${error.message}`);
    }

    // 8. Test memory actions (edit, delete, view)
    console.log('⚙️ Testing memory actions...');
    
    try {
      // Look for action buttons in table rows or cards
      const actionButtons = await page.locator('button').filter({ hasText: /edit|delete|view|more/i }).all();
      const actionMenus = await page.locator('[data-testid="action-menu"], .action-menu').all();
      
      if (actionButtons.length > 0) {
        console.log(`✅ Found ${actionButtons.length} action buttons`);
        
        // Test first action button
        await actionButtons[0].click();
        await page.waitForTimeout(1000);
        
        // Look for dropdown or modal
        const dropdown = await page.locator('.dropdown, [data-testid="dropdown"]').first();
        const modal = await page.locator('.modal, [data-testid="modal"]').first();
        
        if (await dropdown.isVisible() || await modal.isVisible()) {
          console.log('✅ Action menu/modal opened');
          
          // Close it
          await page.keyboard.press('Escape');
          await page.waitForTimeout(500);
        }
        
        testResults.memoryActions.success = true;
      } else {
        testResults.memoryActions.errors.push('No action buttons found');
      }
    } catch (error) {
      testResults.memoryActions.errors.push(`Memory actions test failed: ${error.message}`);
    }

    // 9. Take final screenshot
    console.log('📸 Taking final screenshot...');
    const finalScreenshotPath = '/tmp/memories-page-final-screenshot.png';
    await page.screenshot({ path: finalScreenshotPath, fullPage: true });
    console.log(`✅ Final screenshot saved to: ${finalScreenshotPath}`);

    // Wait a bit more to catch any delayed console errors
    await page.waitForTimeout(2000);

  } catch (error) {
    console.error('❌ Test failed:', error);
    testResults.pageLoad.errors.push(`Page load failed: ${error.message}`);
  } finally {
    await browser.close();
  }

  return testResults;
}

// Run the test
testMemoriesPage().then(results => {
  console.log('\n🎯 TEST RESULTS SUMMARY:');
  console.log('========================');
  
  console.log(`📄 Page Load: ${results.pageLoad.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.pageLoad.errors.length > 0) {
    console.log(`   Errors: ${results.pageLoad.errors.join(', ')}`);
  }
  
  console.log(`📸 Screenshot: ${results.screenshot.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.screenshot.path) {
    console.log(`   Screenshot: ${results.screenshot.path}`);
  }
  
  console.log(`🆕 Create Memory: ${results.createMemory.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.createMemory.errors.length > 0) {
    console.log(`   Errors: ${results.createMemory.errors.join(', ')}`);
  }
  
  console.log(`📋 Form Validation: ${results.formValidation.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.formValidation.errors.length > 0) {
    console.log(`   Errors: ${results.formValidation.errors.join(', ')}`);
  }
  
  console.log(`📊 Table Display: ${results.tableDisplay.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.tableDisplay.errors.length > 0) {
    console.log(`   Errors: ${results.tableDisplay.errors.join(', ')}`);
  }
  
  console.log(`🔍 Filtering: ${results.filtering.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.filtering.errors.length > 0) {
    console.log(`   Errors: ${results.filtering.errors.join(', ')}`);
  }
  
  console.log(`📄 Pagination: ${results.pagination.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.pagination.errors.length > 0) {
    console.log(`   Errors: ${results.pagination.errors.join(', ')}`);
  }
  
  console.log(`⚙️ Memory Actions: ${results.memoryActions.success ? '✅ PASS' : '❌ FAIL'}`);
  if (results.memoryActions.errors.length > 0) {
    console.log(`   Errors: ${results.memoryActions.errors.join(', ')}`);
  }
  
  if (results.consoleErrors.length > 0) {
    console.log(`🚨 Console Errors (${results.consoleErrors.length}):`);
    results.consoleErrors.forEach(error => console.log(`   - ${error}`));
  }
  
  if (results.jsErrors.length > 0) {
    console.log(`🚨 JavaScript Errors (${results.jsErrors.length}):`);
    results.jsErrors.forEach(error => console.log(`   - ${error}`));
  }
  
  console.log('\n✅ Test completed!');
  
  // Save results to file
  const fs = require('fs');
  fs.writeFileSync('/tmp/memories-test-results.json', JSON.stringify(results, null, 2));
  console.log('📄 Detailed results saved to: /tmp/memories-test-results.json');
  
}).catch(error => {
  console.error('❌ Test runner failed:', error);
});