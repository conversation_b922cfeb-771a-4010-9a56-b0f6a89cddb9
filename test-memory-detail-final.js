const { test, expect } = require('@playwright/test');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const API_URL = 'http://localhost:8765';

test.describe('Memory Detail Pages Final Test', () => {
  
  test('Comprehensive memory detail page test', async ({ page }) => {
    console.log('=== STARTING COMPREHENSIVE MEMORY DETAIL PAGE TEST ===');
    
    // Step 1: Get existing memory from API
    console.log('\n1. Fetching memories from API...');
    const apiResponse = await page.request.get(`${API_URL}/api/v1/memories/`);
    const apiData = await apiResponse.json();
    
    console.log(`API Status: ${apiResponse.status()}`);
    console.log(`Total memories: ${apiData.total}`);
    console.log(`Items returned: ${apiData.items?.length || 0}`);
    
    if (!apiData.items || apiData.items.length === 0) {
      throw new Error('No memories found to test');
    }
    
    const testMemory = apiData.items[0];
    console.log(`Using memory ID: ${testMemory.id}`);
    console.log(`Memory content: "${testMemory.content.substring(0, 100)}..."`);
    console.log(`Memory app: ${testMemory.app_name}`);
    console.log(`Memory state: ${testMemory.state}`);
    
    // Step 2: Test single memory API endpoint
    console.log('\n2. Testing single memory API endpoint...');
    const singleMemoryResponse = await page.request.get(`${API_URL}/api/v1/memories/${testMemory.id}`);
    console.log(`Single memory API status: ${singleMemoryResponse.status()}`);
    
    if (singleMemoryResponse.ok()) {
      const singleMemory = await singleMemoryResponse.json();
      console.log(`Single memory ID: ${singleMemory.id}`);
      console.log(`Single memory text: "${singleMemory.text?.substring(0, 50)}..."`);
      console.log(`Single memory state: ${singleMemory.state}`);
    } else {
      console.log(`❌ Single memory API failed`);
    }
    
    // Step 3: Navigate to memory detail page
    console.log('\n3. Navigating to memory detail page...');
    const memoryDetailUrl = `${BASE_URL}/memory/${testMemory.id}`;
    console.log(`URL: ${memoryDetailUrl}`);
    
    await page.goto(memoryDetailUrl);
    
    // Wait for initial load
    await page.waitForLoadState('domcontentloaded');
    console.log('✓ DOM content loaded');
    
    // Wait for network to settle
    await page.waitForLoadState('networkidle', { timeout: 15000 });
    console.log('✓ Network idle');
    
    // Take initial screenshot
    await page.screenshot({ 
      path: 'test-screenshots/memory-detail-initial.png',
      fullPage: true 
    });
    
    // Step 4: Analyze page content
    console.log('\n4. Analyzing page content...');
    const pageText = await page.textContent('body');
    const pageTitle = await page.title();
    const currentUrl = page.url();
    
    console.log(`Page title: ${pageTitle}`);
    console.log(`Current URL: ${currentUrl}`);
    console.log(`Page text length: ${pageText.length}`);
    
    // Check for 404 error
    const has404 = pageText.includes('404') || pageText.toLowerCase().includes('not found');
    console.log(`Contains 404 error: ${has404}`);
    
    if (has404) {
      console.log('\n❌ MEMORY DETAIL PAGE SHOWING 404 ERROR');
      console.log('Taking screenshot for debugging...');
      await page.screenshot({ 
        path: 'test-screenshots/memory-detail-404-error.png',
        fullPage: true 
      });
      
      // Check if it's the custom 404 component
      const custom404 = pageText.includes('Memory not found');
      console.log(`Shows custom 404 message: ${custom404}`);
      
      return; // Exit early for 404 case
    }
    
    // Step 5: Test component presence
    console.log('\n5. Testing component presence...');
    
    const componentTests = [
      { name: 'Back Button', selector: 'button:has-text("Back"), [aria-label*="back"], button:has([class*="arrow"])', expected: true },
      { name: 'Memory ID', selector: `text=${testMemory.id.substring(0, 6)}`, expected: true },
      { name: 'Memory Content', selector: `text="${testMemory.content.substring(0, 30)}"`, expected: true },
      { name: 'Memory Actions', selector: 'button', expected: true },
      { name: 'Copy Button', selector: 'button:has([class*="copy"]), [aria-label*="copy"]', expected: false },
      { name: 'Categories', selector: '.badge, [class*="badge"]', expected: false },
      { name: 'App Name', selector: `text=${testMemory.app_name}`, expected: false },
      { name: 'Access Log Section', selector: 'text="Access Log", [data-testid*="access"], [class*="access"]', expected: false },
      { name: 'Related Memories Section', selector: 'text="Related", [data-testid*="related"], [class*="related"]', expected: false }
    ];
    
    for (const test of componentTests) {
      try {
        const elements = await page.locator(test.selector).count();
        const found = elements > 0;
        console.log(`${test.name}: ${found ? '✓' : '⚠'} (${elements} elements)`);
      } catch (error) {
        console.log(`${test.name}: ❌ Error testing selector`);
      }
    }
    
    // Step 6: Test interactive elements
    console.log('\n6. Testing interactive elements...');
    
    const buttons = await page.locator('button').all();
    console.log(`Found ${buttons.length} buttons`);
    
    for (let i = 0; i < Math.min(buttons.length, 5); i++) {
      try {
        const button = buttons[i];
        const buttonText = await button.textContent();
        const isVisible = await button.isVisible();
        const isEnabled = await button.isEnabled();
        
        console.log(`Button ${i + 1}: "${buttonText}" - Visible: ${isVisible}, Enabled: ${isEnabled}`);
        
        // Test clicking the button
        if (isVisible && isEnabled && buttonText && buttonText.length > 0) {
          console.log(`  Testing click on: "${buttonText}"`);
          await button.click();
          await page.waitForTimeout(1000);
          
          // Check for any modal or navigation
          const newUrl = page.url();
          if (newUrl !== currentUrl) {
            console.log(`  ✓ Navigation to: ${newUrl}`);
            // Navigate back to continue testing
            await page.goto(memoryDetailUrl);
            await page.waitForLoadState('networkidle', { timeout: 10000 });
          } else {
            console.log(`  ✓ Button clicked (no navigation)`);
          }
        }
      } catch (error) {
        console.log(`Button ${i + 1}: Error - ${error.message}`);
      }
    }
    
    // Step 7: Test memory actions
    console.log('\n7. Testing memory actions...');
    
    const actionSelectors = [
      'button:has-text("Edit")',
      'button:has-text("Delete")',
      'button:has-text("Archive")',
      '[data-testid*="edit"]',
      '[data-testid*="delete"]',
      '[data-testid*="archive"]'
    ];
    
    for (const selector of actionSelectors) {
      const elements = await page.locator(selector).count();
      if (elements > 0) {
        console.log(`✓ Found action: ${selector} (${elements} elements)`);
      }
    }
    
    // Step 8: Test modals and forms
    console.log('\n8. Testing modals and forms...');
    
    const modalTriggers = await page.locator('button:has-text("Edit"), button:has-text("Update"), [data-testid*="edit"]').all();
    
    if (modalTriggers.length > 0) {
      console.log(`Found ${modalTriggers.length} potential modal triggers`);
      
      try {
        await modalTriggers[0].click();
        await page.waitForTimeout(1000);
        
        const modals = await page.locator('[role="dialog"], .modal, [data-testid*="modal"]').count();
        if (modals > 0) {
          console.log(`✓ Modal opened (${modals} modals found)`);
          
          // Take screenshot of modal
          await page.screenshot({ 
            path: 'test-screenshots/memory-detail-modal.png',
            fullPage: true 
          });
          
          // Try to close modal
          const closeButtons = await page.locator('button:has-text("Cancel"), button:has-text("Close"), [aria-label*="close"]').all();
          if (closeButtons.length > 0) {
            await closeButtons[0].click();
            console.log(`✓ Modal closed`);
          } else {
            await page.keyboard.press('Escape');
            console.log(`✓ Modal closed with Escape`);
          }
        } else {
          console.log(`⚠ No modal opened after clicking edit button`);
        }
      } catch (error) {
        console.log(`❌ Error testing modal: ${error.message}`);
      }
    }
    
    // Step 9: Test sections and layout
    console.log('\n9. Testing page sections and layout...');
    
    const sections = [
      { name: 'Header/Navigation', selector: 'header, nav, [role="banner"]' },
      { name: 'Main Content', selector: 'main, [role="main"], .container' },
      { name: 'Memory Details Section', selector: '[class*="memory"], [data-testid*="memory"]' },
      { name: 'Sidebar/Secondary Content', selector: 'aside, .sidebar, [class*="sidebar"]' },
      { name: 'Footer', selector: 'footer, [role="contentinfo"]' }
    ];
    
    for (const section of sections) {
      const elements = await page.locator(section.selector).count();
      console.log(`${section.name}: ${elements > 0 ? '✓' : '⚠'} (${elements} elements)`);
    }
    
    // Step 10: Performance and final checks
    console.log('\n10. Final checks and performance...');
    
    const finalPageText = await page.textContent('body');
    const memoryIdInPage = finalPageText.includes(testMemory.id);
    const memoryContentInPage = finalPageText.includes(testMemory.content.substring(0, 30));
    
    console.log(`Memory ID in page: ${memoryIdInPage ? '✓' : '❌'}`);
    console.log(`Memory content in page: ${memoryContentInPage ? '✓' : '❌'}`);
    
    // Take final screenshot
    await page.screenshot({ 
      path: 'test-screenshots/memory-detail-final.png',
      fullPage: true 
    });
    
    // Generate test report
    console.log('\n=== MEMORY DETAIL PAGE TEST REPORT ===');
    console.log(`Test Memory ID: ${testMemory.id}`);
    console.log(`Memory Content: "${testMemory.content.substring(0, 100)}..."`);
    console.log(`Page Title: ${pageTitle}`);
    console.log(`Final URL: ${page.url()}`);
    console.log(`Page Loaded Successfully: ${!has404 ? '✅' : '❌'}`);
    console.log(`Memory ID Found: ${memoryIdInPage ? '✅' : '❌'}`);
    console.log(`Memory Content Found: ${memoryContentInPage ? '✅' : '❌'}`);
    console.log(`Interactive Elements: ${buttons.length} buttons found`);
    console.log(`Modal Functionality: ${modalTriggers.length > 0 ? '✅' : '⚠'}`);
    
    if (!has404 && (memoryIdInPage || memoryContentInPage)) {
      console.log('\n🎉 MEMORY DETAIL PAGE TEST PASSED!');
    } else {
      console.log('\n❌ MEMORY DETAIL PAGE TEST FAILED!');
      if (has404) {
        console.log('   - Page shows 404 error');
      }
      if (!memoryIdInPage) {
        console.log('   - Memory ID not found in page');
      }
      if (!memoryContentInPage) {
        console.log('   - Memory content not found in page');
      }
    }
    
    console.log('=== END OF TEST ===\n');
  });
  
});