const { test, expect } = require('@playwright/test');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const API_URL = 'http://localhost:8765';

test.describe('Memory Detail Pages Manual Test', () => {
  
  test('Test memory detail page with manual navigation', async ({ page }) => {
    console.log('Starting manual memory detail test...');
    
    // First, get a memory from the API
    const apiResponse = await page.request.get(`${API_URL}/api/v1/memories/`);
    const apiData = await apiResponse.json();
    
    console.log(`API Response status: ${apiResponse.status()}`);
    console.log(`Total memories found: ${apiData.total}`);
    console.log(`Items in response: ${apiData.items?.length || 0}`);
    
    if (!apiData.items || apiData.items.length === 0) {
      throw new Error('No memories found via API');
    }
    
    const memory = apiData.items[0];
    console.log(`First memory ID: ${memory.id}`);
    console.log(`First memory content: ${memory.content.substring(0, 100)}...`);
    
    // Test direct API call to specific memory
    const singleMemoryResponse = await page.request.get(`${API_URL}/api/v1/memories/${memory.id}`);
    console.log(`Single memory API response status: ${singleMemoryResponse.status()}`);
    
    if (singleMemoryResponse.ok()) {
      const singleMemory = await singleMemoryResponse.json();
      console.log(`Single memory retrieved: ${singleMemory.id}`);
    }
    
    // Navigate to the memory detail page
    const memoryDetailUrl = `${BASE_URL}/memory/${memory.id}`;
    console.log(`Navigating to: ${memoryDetailUrl}`);
    
    await page.goto(memoryDetailUrl);
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle', { timeout: 10000 });
    
    // Take a screenshot
    await page.screenshot({ 
      path: 'test-screenshots/memory-detail-manual.png',
      fullPage: true 
    });
    
    // Check the page content
    const pageText = await page.textContent('body');
    console.log(`Page text length: ${pageText.length}`);
    
    // Check for 404 error
    const has404 = pageText.includes('404') || pageText.includes('not found');
    console.log(`Contains 404 error: ${has404}`);
    
    // Check for the memory ID in the page
    const hasMemoryId = pageText.includes(memory.id);
    console.log(`Contains memory ID: ${hasMemoryId}`);
    
    // Check page title
    const pageTitle = await page.title();
    console.log(`Page title: ${pageTitle}`);
    
    // Check URL after navigation
    const currentUrl = page.url();
    console.log(`Current URL: ${currentUrl}`);
    
    // Look for specific elements
    const memoryContentElements = await page.locator('div').count();
    console.log(`Number of div elements: ${memoryContentElements}`);
    
    const buttons = await page.locator('button').count();
    console.log(`Number of buttons: ${buttons}`);
    
    // Check for loading states
    const loadingElements = await page.locator('[data-testid*="loading"], .loading, .skeleton').count();
    console.log(`Loading elements found: ${loadingElements}`);
    
    // Check for error messages
    const errorElements = await page.locator('[data-testid*="error"], .error').count();
    console.log(`Error elements found: ${errorElements}`);
    
    // Wait a bit more and check again
    await page.waitForTimeout(2000);
    
    const finalPageText = await page.textContent('body');
    const finalHas404 = finalPageText.includes('404') || finalPageText.includes('not found');
    console.log(`Final check - Contains 404 error: ${finalHas404}`);
    
    // Report results
    console.log('\n=== MEMORY DETAIL PAGE TEST RESULTS ===');
    console.log(`Memory ID tested: ${memory.id}`);
    console.log(`Memory content preview: ${memory.content.substring(0, 100)}...`);
    console.log(`Page loaded successfully: ${!finalHas404}`);
    console.log(`Memory ID found in page: ${hasMemoryId}`);
    console.log(`Page title: ${pageTitle}`);
    console.log(`Current URL: ${currentUrl}`);
    console.log(`Total elements on page: ${memoryContentElements}`);
    console.log(`Interactive buttons: ${buttons}`);
    
    if (finalHas404) {
      console.log('⚠️  WARNING: Memory detail page showing 404 error');
      console.log('This suggests the frontend cannot find the memory or there is a routing issue');
    } else {
      console.log('✅ Memory detail page loaded successfully');
    }
  });
  
  test('Test memory detail page components', async ({ page }) => {
    console.log('Testing memory detail page components...');
    
    // Get a memory from API
    const apiResponse = await page.request.get(`${API_URL}/api/v1/memories/`);
    const apiData = await apiResponse.json();
    
    if (!apiData.items || apiData.items.length === 0) {
      throw new Error('No memories found for component testing');
    }
    
    const memory = apiData.items[0];
    console.log(`Testing components for memory: ${memory.id}`);
    
    // Navigate to memory detail page
    await page.goto(`${BASE_URL}/memory/${memory.id}`);
    await page.waitForLoadState('networkidle', { timeout: 10000 });
    
    // Check if it's a 404 page
    const pageText = await page.textContent('body');
    if (pageText.includes('404') || pageText.includes('not found')) {
      console.log('⚠️  Page shows 404 - skipping component tests');
      return;
    }
    
    // Test various component selectors
    const componentTests = [
      { name: 'Navigation', selector: 'nav, header, [role="navigation"]' },
      { name: 'Main Content', selector: 'main, [role="main"]' },
      { name: 'Memory Content', selector: '[data-testid*="memory"], .memory-content' },
      { name: 'Action Buttons', selector: 'button' },
      { name: 'Links', selector: 'a' },
      { name: 'Forms', selector: 'form' },
      { name: 'Inputs', selector: 'input, textarea' },
      { name: 'Cards', selector: '.card, [data-testid*="card"]' },
      { name: 'Modals', selector: '[role="dialog"], .modal' },
      { name: 'Tables', selector: 'table' }
    ];
    
    for (const test of componentTests) {
      const elements = await page.locator(test.selector).count();
      console.log(`${test.name}: ${elements} elements found`);
    }
    
    // Test interactive elements
    const interactiveElements = await page.locator('button, a, input, textarea, select').all();
    console.log(`\nInteractive elements found: ${interactiveElements.length}`);
    
    for (let i = 0; i < Math.min(interactiveElements.length, 5); i++) {
      try {
        const element = interactiveElements[i];
        const tagName = await element.evaluate(el => el.tagName);
        const text = await element.textContent();
        const isVisible = await element.isVisible();
        const isEnabled = await element.isEnabled();
        
        console.log(`Element ${i + 1}: ${tagName} - "${text}" - Visible: ${isVisible}, Enabled: ${isEnabled}`);
      } catch (error) {
        console.log(`Element ${i + 1}: Error getting info - ${error.message}`);
      }
    }
    
    // Take detailed screenshot
    await page.screenshot({ 
      path: 'test-screenshots/memory-detail-components.png',
      fullPage: true 
    });
    
    console.log('✅ Component testing completed');
  });
  
  test('Test memory API endpoints', async ({ page }) => {
    console.log('Testing memory API endpoints...');
    
    // Test the memories list endpoint
    const listResponse = await page.request.get(`${API_URL}/api/v1/memories/`);
    console.log(`Memories list API status: ${listResponse.status()}`);
    
    if (listResponse.ok()) {
      const listData = await listResponse.json();
      console.log(`Total memories in API: ${listData.total}`);
      console.log(`Items returned: ${listData.items?.length || 0}`);
      console.log(`Pages: ${listData.pages}`);
      
      if (listData.items && listData.items.length > 0) {
        const memory = listData.items[0];
        console.log(`First memory ID: ${memory.id}`);
        console.log(`First memory app: ${memory.app_name}`);
        console.log(`First memory state: ${memory.state}`);
        
        // Test single memory endpoint
        const singleResponse = await page.request.get(`${API_URL}/api/v1/memories/${memory.id}`);
        console.log(`Single memory API status: ${singleResponse.status()}`);
        
        if (singleResponse.ok()) {
          const singleData = await singleResponse.json();
          console.log(`Single memory data retrieved: ${singleData.id}`);
          console.log(`Single memory content length: ${singleData.content?.length || 0}`);
        } else {
          console.log(`❌ Single memory API failed with status: ${singleResponse.status()}`);
        }
      }
    } else {
      console.log(`❌ Memories list API failed with status: ${listResponse.status()}`);
    }
    
    console.log('✅ API endpoint testing completed');
  });
  
});