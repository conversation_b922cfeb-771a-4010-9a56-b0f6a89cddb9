const { test, expect } = require('@playwright/test');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const API_URL = 'http://localhost:8765';

// Test data
let testMemoryId = null;
let existingMemoryId = null;

test.describe('Memory Detail Pages Test Suite', () => {
  test.beforeAll(async ({ request }) => {
    // Check for existing memories
    const response = await request.get(`${API_URL}/api/v1/memories/`);
    const data = await response.json();
    
    if (data.items && data.items.length > 0) {
      existingMemoryId = data.items[0].id;
      console.log(`Found existing memory with ID: ${existingMemoryId}`);
    }
    
    // Create a test memory for comprehensive testing
    try {
      const createResponse = await request.post(`${API_URL}/api/v1/memories/`, {
        data: {
          content: "This is a test memory for Playwright automation testing. It contains detailed information about testing memory detail pages, including content display, metadata verification, and interactive functionality testing.",
          app_id: "test-app-id",
          app_name: "playwright-test",
          categories: ["testing", "automation", "qa"],
          metadata: {
            test_type: "playwright",
            created_by: "automation",
            priority: "high"
          }
        }
      });
      
      if (createResponse.ok()) {
        const createdMemory = await createResponse.json();
        testMemoryId = createdMemory.id;
        console.log(`Created test memory with ID: ${testMemoryId}`);
      }
    } catch (error) {
      console.log(`Could not create test memory: ${error.message}`);
    }
  });

  test('Navigate to memory detail page and verify basic loading', async ({ page }) => {
    const memoryId = testMemoryId || existingMemoryId;
    
    if (!memoryId) {
      throw new Error('No memory ID available for testing');
    }

    // Navigate to memory detail page
    await page.goto(`${BASE_URL}/memory/${memoryId}`);
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-screenshots/memory-detail-page.png',
      fullPage: true 
    });
    
    // Verify page loaded correctly
    await expect(page).toHaveTitle(/Memory Detail|Memory/);
    
    // Check for basic page structure
    await expect(page.locator('body')).toBeVisible();
    
    console.log(`✓ Successfully navigated to memory detail page: ${memoryId}`);
  });

  test('Verify memory content display', async ({ page }) => {
    const memoryId = testMemoryId || existingMemoryId;
    
    await page.goto(`${BASE_URL}/memory/${memoryId}`);
    await page.waitForLoadState('networkidle');
    
    // Check for memory content section
    const contentSelectors = [
      '[data-testid="memory-content"]',
      '.memory-content',
      'main',
      '[role="main"]'
    ];
    
    let contentFound = false;
    for (const selector of contentSelectors) {
      const element = page.locator(selector);
      if (await element.count() > 0) {
        await expect(element).toBeVisible();
        contentFound = true;
        console.log(`✓ Found memory content with selector: ${selector}`);
        break;
      }
    }
    
    if (!contentFound) {
      // Take screenshot for debugging
      await page.screenshot({ 
        path: 'test-screenshots/memory-content-not-found.png',
        fullPage: true 
      });
      console.log('⚠ Memory content section not found with common selectors');
    }
    
    // Check for any text content on the page
    const bodyText = await page.textContent('body');
    expect(bodyText.length).toBeGreaterThan(0);
    
    console.log(`✓ Memory content display verified`);
  });

  test('Verify memory metadata display', async ({ page }) => {
    const memoryId = testMemoryId || existingMemoryId;
    
    await page.goto(`${BASE_URL}/memory/${memoryId}`);
    await page.waitForLoadState('networkidle');
    
    // Check for metadata elements
    const metadataSelectors = [
      '[data-testid="memory-metadata"]',
      '.memory-metadata',
      '[data-testid="created-date"]',
      '[data-testid="updated-date"]',
      '.metadata',
      '.memory-info'
    ];
    
    let metadataFound = false;
    for (const selector of metadataSelectors) {
      const element = page.locator(selector);
      if (await element.count() > 0) {
        await expect(element).toBeVisible();
        metadataFound = true;
        console.log(`✓ Found memory metadata with selector: ${selector}`);
        break;
      }
    }
    
    // Look for date-related content
    const datePatterns = [
      /\d{4}-\d{2}-\d{2}/,  // YYYY-MM-DD
      /\d{1,2}\/\d{1,2}\/\d{4}/,  // MM/DD/YYYY
      /\d{1,2}-\d{1,2}-\d{4}/,  // MM-DD-YYYY
      /Created|Updated|Modified/i
    ];
    
    const bodyText = await page.textContent('body');
    let dateFound = false;
    for (const pattern of datePatterns) {
      if (pattern.test(bodyText)) {
        dateFound = true;
        console.log(`✓ Found date pattern: ${pattern}`);
        break;
      }
    }
    
    if (!metadataFound && !dateFound) {
      await page.screenshot({ 
        path: 'test-screenshots/memory-metadata-not-found.png',
        fullPage: true 
      });
      console.log('⚠ Memory metadata not found with common selectors');
    }
    
    console.log(`✓ Memory metadata display verified`);
  });

  test('Verify memory actions (edit, delete, archive)', async ({ page }) => {
    const memoryId = testMemoryId || existingMemoryId;
    
    await page.goto(`${BASE_URL}/memory/${memoryId}`);
    await page.waitForLoadState('networkidle');
    
    // Check for action buttons
    const actionSelectors = [
      '[data-testid="edit-button"]',
      '[data-testid="delete-button"]',
      '[data-testid="archive-button"]',
      'button:has-text("Edit")',
      'button:has-text("Delete")',
      'button:has-text("Archive")',
      '.memory-actions button',
      '[role="button"]'
    ];
    
    let actionsFound = [];
    for (const selector of actionSelectors) {
      const elements = page.locator(selector);
      const count = await elements.count();
      if (count > 0) {
        actionsFound.push({ selector, count });
        console.log(`✓ Found ${count} action element(s) with selector: ${selector}`);
      }
    }
    
    // Look for any buttons on the page
    const allButtons = page.locator('button');
    const buttonCount = await allButtons.count();
    console.log(`✓ Found ${buttonCount} total buttons on the page`);
    
    if (buttonCount > 0) {
      // Take screenshot showing the buttons
      await page.screenshot({ 
        path: 'test-screenshots/memory-actions-buttons.png',
        fullPage: true 
      });
      
      // Try to get button text content
      for (let i = 0; i < Math.min(buttonCount, 10); i++) {
        try {
          const button = allButtons.nth(i);
          const buttonText = await button.textContent();
          console.log(`Button ${i + 1}: "${buttonText}"`);
        } catch (error) {
          console.log(`Button ${i + 1}: Could not get text content`);
        }
      }
    }
    
    if (actionsFound.length === 0 && buttonCount === 0) {
      await page.screenshot({ 
        path: 'test-screenshots/memory-actions-not-found.png',
        fullPage: true 
      });
      console.log('⚠ Memory action buttons not found');
    }
    
    console.log(`✓ Memory actions verification completed`);
  });

  test('Verify related memories section', async ({ page }) => {
    const memoryId = testMemoryId || existingMemoryId;
    
    await page.goto(`${BASE_URL}/memory/${memoryId}`);
    await page.waitForLoadState('networkidle');
    
    // Check for related memories section
    const relatedSelectors = [
      '[data-testid="related-memories"]',
      '.related-memories',
      'section:has-text("Related")',
      'div:has-text("Related")',
      '.related-section'
    ];
    
    let relatedFound = false;
    for (const selector of relatedSelectors) {
      const element = page.locator(selector);
      if (await element.count() > 0) {
        await expect(element).toBeVisible();
        relatedFound = true;
        console.log(`✓ Found related memories section with selector: ${selector}`);
        break;
      }
    }
    
    // Look for text mentioning "related"
    const bodyText = await page.textContent('body');
    const hasRelatedText = /related/i.test(bodyText);
    
    if (!relatedFound && !hasRelatedText) {
      await page.screenshot({ 
        path: 'test-screenshots/related-memories-not-found.png',
        fullPage: true 
      });
      console.log('⚠ Related memories section not found');
    } else if (hasRelatedText) {
      console.log(`✓ Found "related" text in page content`);
    }
    
    console.log(`✓ Related memories section verification completed`);
  });

  test('Verify access log section', async ({ page }) => {
    const memoryId = testMemoryId || existingMemoryId;
    
    await page.goto(`${BASE_URL}/memory/${memoryId}`);
    await page.waitForLoadState('networkidle');
    
    // Check for access log section
    const accessLogSelectors = [
      '[data-testid="access-log"]',
      '.access-log',
      'section:has-text("Access")',
      'div:has-text("Access")',
      'section:has-text("Log")',
      'div:has-text("Log")',
      '.log-section'
    ];
    
    let accessLogFound = false;
    for (const selector of accessLogSelectors) {
      const element = page.locator(selector);
      if (await element.count() > 0) {
        await expect(element).toBeVisible();
        accessLogFound = true;
        console.log(`✓ Found access log section with selector: ${selector}`);
        break;
      }
    }
    
    // Look for text mentioning "access" or "log"
    const bodyText = await page.textContent('body');
    const hasAccessText = /access|log/i.test(bodyText);
    
    if (!accessLogFound && !hasAccessText) {
      await page.screenshot({ 
        path: 'test-screenshots/access-log-not-found.png',
        fullPage: true 
      });
      console.log('⚠ Access log section not found');
    } else if (hasAccessText) {
      console.log(`✓ Found "access" or "log" text in page content`);
    }
    
    console.log(`✓ Access log section verification completed`);
  });

  test('Test interactive elements and modal dialogs', async ({ page }) => {
    const memoryId = testMemoryId || existingMemoryId;
    
    await page.goto(`${BASE_URL}/memory/${memoryId}`);
    await page.waitForLoadState('networkidle');
    
    // Find and test clickable elements
    const clickableElements = await page.locator('button, [role="button"], a, input[type="button"], input[type="submit"]').all();
    
    console.log(`Found ${clickableElements.length} clickable elements`);
    
    for (let i = 0; i < Math.min(clickableElements.length, 5); i++) {
      try {
        const element = clickableElements[i];
        const elementText = await element.textContent();
        console.log(`Testing clickable element ${i + 1}: "${elementText}"`);
        
        // Click the element
        await element.click();
        
        // Wait a bit for any modal or dialog to appear
        await page.waitForTimeout(1000);
        
        // Check for modal/dialog elements
        const modalSelectors = [
          '[role="dialog"]',
          '.modal',
          '.dialog',
          '[data-testid="modal"]',
          '.overlay'
        ];
        
        let modalFound = false;
        for (const selector of modalSelectors) {
          const modal = page.locator(selector);
          if (await modal.count() > 0) {
            modalFound = true;
            console.log(`✓ Modal/dialog opened with selector: ${selector}`);
            
            // Take screenshot of modal
            await page.screenshot({ 
              path: `test-screenshots/modal-${i + 1}.png`,
              fullPage: true 
            });
            
            // Try to close modal (look for close button, cancel, or escape)
            const closeSelectors = [
              '[data-testid="close-button"]',
              'button:has-text("Close")',
              'button:has-text("Cancel")',
              '[aria-label="Close"]',
              '.close-button'
            ];
            
            let modalClosed = false;
            for (const closeSelector of closeSelectors) {
              const closeButton = page.locator(closeSelector);
              if (await closeButton.count() > 0) {
                await closeButton.click();
                modalClosed = true;
                console.log(`✓ Modal closed using: ${closeSelector}`);
                break;
              }
            }
            
            if (!modalClosed) {
              // Try pressing Escape key
              await page.keyboard.press('Escape');
              console.log(`✓ Attempted to close modal with Escape key`);
            }
            
            break;
          }
        }
        
        if (!modalFound) {
          // Check if page navigation occurred
          const currentUrl = page.url();
          if (currentUrl !== `${BASE_URL}/memory/${memoryId}`) {
            console.log(`✓ Navigation occurred to: ${currentUrl}`);
            // Navigate back
            await page.goto(`${BASE_URL}/memory/${memoryId}`);
            await page.waitForLoadState('networkidle');
          }
        }
        
      } catch (error) {
        console.log(`Error testing clickable element ${i + 1}: ${error.message}`);
      }
    }
    
    console.log(`✓ Interactive elements testing completed`);
  });

  test('Test edit memory functionality', async ({ page }) => {
    const memoryId = testMemoryId || existingMemoryId;
    
    await page.goto(`${BASE_URL}/memory/${memoryId}`);
    await page.waitForLoadState('networkidle');
    
    // Look for edit button or edit functionality
    const editSelectors = [
      '[data-testid="edit-button"]',
      'button:has-text("Edit")',
      '[aria-label="Edit"]',
      '.edit-button',
      'button[title*="Edit"]'
    ];
    
    let editFound = false;
    for (const selector of editSelectors) {
      const editButton = page.locator(selector);
      if (await editButton.count() > 0) {
        console.log(`✓ Found edit button with selector: ${selector}`);
        
        try {
          await editButton.click();
          await page.waitForTimeout(1000);
          
          // Look for edit form or modal
          const editFormSelectors = [
            'form',
            '[data-testid="edit-form"]',
            'textarea',
            'input[type="text"]',
            '.edit-form'
          ];
          
          let editFormFound = false;
          for (const formSelector of editFormSelectors) {
            const form = page.locator(formSelector);
            if (await form.count() > 0) {
              editFormFound = true;
              console.log(`✓ Found edit form with selector: ${formSelector}`);
              
              // Take screenshot of edit form
              await page.screenshot({ 
                path: 'test-screenshots/edit-form.png',
                fullPage: true 
              });
              
              // If it's a textarea or input, try to edit
              if (formSelector.includes('textarea') || formSelector.includes('input')) {
                await form.first().clear();
                await form.first().fill('Updated memory content for testing');
                console.log(`✓ Updated content in edit form`);
              }
              
              break;
            }
          }
          
          if (!editFormFound) {
            console.log(`⚠ Edit form not found after clicking edit button`);
          }
          
          editFound = true;
          break;
        } catch (error) {
          console.log(`Error clicking edit button: ${error.message}`);
        }
      }
    }
    
    if (!editFound) {
      console.log(`⚠ Edit functionality not found`);
    }
    
    console.log(`✓ Edit memory functionality testing completed`);
  });

  test('Test delete memory functionality', async ({ page }) => {
    // Only test delete with the created test memory, not existing ones
    if (!testMemoryId) {
      console.log('⚠ Skipping delete test - no test memory created');
      return;
    }
    
    await page.goto(`${BASE_URL}/memory/${testMemoryId}`);
    await page.waitForLoadState('networkidle');
    
    // Look for delete button
    const deleteSelectors = [
      '[data-testid="delete-button"]',
      'button:has-text("Delete")',
      '[aria-label="Delete"]',
      '.delete-button',
      'button[title*="Delete"]'
    ];
    
    let deleteFound = false;
    for (const selector of deleteSelectors) {
      const deleteButton = page.locator(selector);
      if (await deleteButton.count() > 0) {
        console.log(`✓ Found delete button with selector: ${selector}`);
        
        try {
          await deleteButton.click();
          await page.waitForTimeout(1000);
          
          // Look for confirmation dialog
          const confirmSelectors = [
            'button:has-text("Confirm")',
            'button:has-text("Yes")',
            'button:has-text("Delete")',
            '[data-testid="confirm-delete"]'
          ];
          
          let confirmFound = false;
          for (const confirmSelector of confirmSelectors) {
            const confirmButton = page.locator(confirmSelector);
            if (await confirmButton.count() > 0) {
              confirmFound = true;
              console.log(`✓ Found confirmation button with selector: ${confirmSelector}`);
              
              // Take screenshot of confirmation dialog
              await page.screenshot({ 
                path: 'test-screenshots/delete-confirmation.png',
                fullPage: true 
              });
              
              // Click confirm (only for test memory)
              await confirmButton.click();
              await page.waitForTimeout(2000);
              
              console.log(`✓ Confirmed delete action`);
              break;
            }
          }
          
          if (!confirmFound) {
            console.log(`⚠ Delete confirmation dialog not found`);
          }
          
          deleteFound = true;
          break;
        } catch (error) {
          console.log(`Error testing delete functionality: ${error.message}`);
        }
      }
    }
    
    if (!deleteFound) {
      console.log(`⚠ Delete functionality not found`);
    }
    
    console.log(`✓ Delete memory functionality testing completed`);
  });

  test('Test error handling and validation', async ({ page }) => {
    // Test with invalid memory ID
    await page.goto(`${BASE_URL}/memory/invalid-id-12345`);
    await page.waitForLoadState('networkidle');
    
    // Check for error message or 404 page
    const errorSelectors = [
      '[data-testid="error-message"]',
      '.error-message',
      'h1:has-text("404")',
      'h1:has-text("Not Found")',
      'div:has-text("error")',
      'div:has-text("not found")'
    ];
    
    let errorFound = false;
    for (const selector of errorSelectors) {
      const errorElement = page.locator(selector);
      if (await errorElement.count() > 0) {
        errorFound = true;
        console.log(`✓ Found error handling with selector: ${selector}`);
        break;
      }
    }
    
    // Check page title for error indication
    const title = await page.title();
    if (title.toLowerCase().includes('error') || title.toLowerCase().includes('not found')) {
      errorFound = true;
      console.log(`✓ Found error indication in page title: ${title}`);
    }
    
    // Check for proper HTTP status
    const response = await page.goto(`${BASE_URL}/memory/invalid-id-12345`);
    if (response && response.status() === 404) {
      errorFound = true;
      console.log(`✓ Proper 404 status code returned`);
    }
    
    // Take screenshot of error page
    await page.screenshot({ 
      path: 'test-screenshots/error-page.png',
      fullPage: true 
    });
    
    if (!errorFound) {
      console.log(`⚠ Error handling not found for invalid memory ID`);
    }
    
    console.log(`✓ Error handling and validation testing completed`);
  });

  test('Performance and loading test', async ({ page }) => {
    const memoryId = testMemoryId || existingMemoryId;
    
    // Measure page load time
    const startTime = Date.now();
    await page.goto(`${BASE_URL}/memory/${memoryId}`);
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    console.log(`✓ Page load time: ${loadTime}ms`);
    
    // Check for loading indicators
    const loadingSelectors = [
      '[data-testid="loading"]',
      '.loading',
      '.spinner',
      '[aria-label="Loading"]'
    ];
    
    let loadingFound = false;
    for (const selector of loadingSelectors) {
      const loadingElement = page.locator(selector);
      if (await loadingElement.count() > 0) {
        loadingFound = true;
        console.log(`✓ Found loading indicator with selector: ${selector}`);
        break;
      }
    }
    
    // Check for skeleton loading
    const skeletonSelectors = [
      '.skeleton',
      '[data-testid="skeleton"]',
      '.loading-skeleton'
    ];
    
    let skeletonFound = false;
    for (const selector of skeletonSelectors) {
      const skeletonElement = page.locator(selector);
      if (await skeletonElement.count() > 0) {
        skeletonFound = true;
        console.log(`✓ Found skeleton loading with selector: ${selector}`);
        break;
      }
    }
    
    if (!loadingFound && !skeletonFound) {
      console.log(`⚠ No loading indicators found`);
    }
    
    console.log(`✓ Performance and loading test completed`);
  });

  test.afterAll(async ({ request }) => {
    // Clean up test memory if it was created
    if (testMemoryId) {
      try {
        await request.delete(`${API_URL}/api/v1/memories/${testMemoryId}`);
        console.log(`✓ Cleaned up test memory: ${testMemoryId}`);
      } catch (error) {
        console.log(`⚠ Could not clean up test memory: ${error.message}`);
      }
    }
  });
});