const { test, expect } = require('@playwright/test');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const API_URL = 'http://localhost:8765';

test.describe('Memory Detail Pages - Simple Test', () => {
  test('Check if memory detail page loads', async ({ page }) => {
    console.log('Starting memory detail page test...');
    
    // First, get an existing memory ID
    const response = await page.request.get(`${API_URL}/api/v1/memories/`);
    const data = await response.json();
    
    if (!data.items || data.items.length === 0) {
      throw new Error('No memories found in the system');
    }
    
    const memoryId = data.items[0].id;
    console.log(`Using memory ID: ${memoryId}`);
    
    // Navigate to memory detail page
    console.log(`Navigating to: ${BASE_URL}/memory/${memoryId}`);
    await page.goto(`${BASE_URL}/memory/${memoryId}`);
    
    // Wait for page to load
    await page.waitForLoadState('networkidle', { timeout: 30000 });
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-screenshots/memory-detail-simple.png',
      fullPage: true 
    });
    
    // Check if page loaded (has some content)
    const bodyText = await page.textContent('body');
    console.log(`Page body length: ${bodyText.length}`);
    
    // Basic verification
    expect(bodyText.length).toBeGreaterThan(0);
    console.log('✓ Memory detail page loaded successfully');
    
    // Check for common elements
    const hasContent = bodyText.length > 100;
    const hasMemoryId = bodyText.includes(memoryId);
    
    console.log(`Has substantial content: ${hasContent}`);
    console.log(`Contains memory ID: ${hasMemoryId}`);
    
    // Look for navigation elements
    const navElements = await page.locator('nav, header, .navbar, [role="navigation"]').count();
    console.log(`Navigation elements found: ${navElements}`);
    
    // Look for buttons
    const buttons = await page.locator('button').count();
    console.log(`Buttons found: ${buttons}`);
    
    // Check page title
    const title = await page.title();
    console.log(`Page title: ${title}`);
    
    console.log('✓ Simple memory detail test completed successfully');
  });
});