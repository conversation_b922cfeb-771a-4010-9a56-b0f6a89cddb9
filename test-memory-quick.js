const { test, expect } = require('@playwright/test');

test('Quick memory detail page test', async ({ page }) => {
  console.log('Starting quick memory detail test...');
  
  const BASE_URL = 'http://localhost:3000';
  const API_URL = 'http://localhost:8765';
  
  // Get memory from API
  const response = await page.request.get(`${API_URL}/api/v1/memories/`);
  const data = await response.json();
  
  if (!data.items || data.items.length === 0) {
    throw new Error('No memories found');
  }
  
  const memory = data.items[0];
  console.log(`Testing memory: ${memory.id}`);
  
  // Navigate to memory detail page
  await page.goto(`${BASE_URL}/memory/${memory.id}`);
  await page.waitForLoadState('networkidle', { timeout: 10000 });
  
  // Take screenshot
  await page.screenshot({ path: 'test-screenshots/memory-detail-quick.png', fullPage: true });
  
  // Check page content
  const pageText = await page.textContent('body');
  const has404 = pageText.includes('404') || pageText.includes('not found');
  const hasMemoryId = pageText.includes(memory.id);
  const hasContent = pageText.includes(memory.content.substring(0, 20));
  
  console.log(`Has 404 error: ${has404}`);
  console.log(`Has memory ID: ${hasMemoryId}`);
  console.log(`Has memory content: ${hasContent}`);
  
  if (!has404) {
    console.log('✅ Memory detail page loaded successfully');
  } else {
    console.log('❌ Memory detail page shows 404 error');
  }
  
  // Count elements
  const buttons = await page.locator('button').count();
  const links = await page.locator('a').count();
  const divs = await page.locator('div').count();
  
  console.log(`Buttons: ${buttons}, Links: ${links}, Divs: ${divs}`);
  console.log('Quick test completed');
});