const { test, expect } = require('@playwright/test');

const BASE_URL = 'http://localhost:3000';

test.describe('Navigation and UI Components - Simple Test', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
  });

  test('should load main page and navigate to all pages', async ({ page }) => {
    // Test homepage
    await expect(page.locator('header')).toBeVisible();
    await expect(page.locator('text=MemoryMaster')).toBeVisible();
    
    // Test navigation to Memories page
    await page.click('a[href="/memories"]');
    await page.waitForLoadState('networkidle');
    expect(page.url()).toContain('/memories');
    
    // Test navigation to Evolution page
    await page.click('a[href="/evolution"]');
    await page.waitForLoadState('networkidle');
    expect(page.url()).toContain('/evolution');
    
    // Test navigation to Settings page
    await page.click('a[href="/settings"]');
    await page.waitForLoadState('networkidle');
    expect(page.url()).toContain('/settings');
    
    // Test navigation back to Dashboard
    await page.click('a[href="/"]');
    await page.waitForLoadState('networkidle');
    expect(page.url()).toBe(BASE_URL + '/');
  });

  test('should test theme toggle', async ({ page }) => {
    const themeToggle = page.locator('button:has(svg)').last();
    await expect(themeToggle).toBeVisible();
    await themeToggle.click();
    await page.waitForTimeout(500);
  });

  test('should test responsive navigation', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Check that navigation is still visible
    await expect(page.locator('header')).toBeVisible();
    await expect(page.locator('a[href="/memories"]')).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    await expect(page.locator('header')).toBeVisible();
  });

  test('should test 404 page', async ({ page }) => {
    await page.goto(BASE_URL + '/nonexistent-page');
    await page.waitForLoadState('networkidle');
    
    // Check for 404 page content
    await expect(page.locator('text=404')).toBeVisible();
  });

  test('should check for console errors', async ({ page }) => {
    const consoleErrors = [];
    
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Visit all main pages
    const pages = ['/', '/memories', '/evolution', '/settings'];
    
    for (const href of pages) {
      await page.goto(BASE_URL + href);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000);
    }
    
    // Log console errors but don't fail the test
    if (consoleErrors.length > 0) {
      console.log('Console errors found:', consoleErrors);
    }
  });
});