const { test, expect } = require('@playwright/test');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TIMEOUT = 30000;

// Test data
const testMemory = {
  content: 'This is a test memory for navigation testing',
  category: 'general',
  metadata: { test: true }
};

// Helper functions
const waitForPageLoad = async (page) => {
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(1000); // Additional wait for React hydration
};

const checkAccessibility = async (page, pageName) => {
  // Check for basic accessibility features
  const issues = [];
  
  // Check for missing alt text on images
  const images = await page.locator('img').all();
  for (const img of images) {
    const alt = await img.getAttribute('alt');
    if (!alt || alt.trim() === '') {
      const src = await img.getAttribute('src');
      issues.push(`Missing alt text on image: ${src}`);
    }
  }
  
  // Check for proper heading structure
  const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
  if (headings.length === 0) {
    issues.push(`No headings found on ${pageName}`);
  }
  
  // Check for form labels
  const inputs = await page.locator('input:not([type="hidden"])').all();
  for (const input of inputs) {
    const id = await input.getAttribute('id');
    const ariaLabel = await input.getAttribute('aria-label');
    const ariaLabelledBy = await input.getAttribute('aria-labelledby');
    
    if (id) {
      const label = await page.locator(`label[for="${id}"]`).count();
      if (label === 0 && !ariaLabel && !ariaLabelledBy) {
        issues.push(`Input without label: ${id}`);
      }
    }
  }
  
  return issues;
};

const checkConsoleErrors = async (page, pageName) => {
  const errors = [];
  
  page.on('console', (msg) => {
    if (msg.type() === 'error') {
      errors.push(`Console error on ${pageName}: ${msg.text()}`);
    }
  });
  
  return errors;
};

// Main test suite
test.describe('Navigation and UI Components Comprehensive Testing', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up console error monitoring
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        console.log(`Console error: ${msg.text()}`);
      }
    });
    
    // Set up network error monitoring
    page.on('response', (response) => {
      if (response.status() >= 400) {
        console.log(`Network error: ${response.status()} ${response.url()}`);
      }
    });
    
    await page.goto(BASE_URL);
    await waitForPageLoad(page);
  });

  test.describe('Main Navigation Menu', () => {
    
    test('should navigate between all pages', async ({ page }) => {
      const navigationTests = [
        { name: 'Dashboard', href: '/', expectedText: 'Dashboard' },
        { name: 'Memories', href: '/memories', expectedText: 'Memories' },
        { name: 'Evolution', href: '/evolution', expectedText: 'Evolution' },
        { name: 'Settings', href: '/settings', expectedText: 'Settings' }
      ];
      
      for (const nav of navigationTests) {
        await test.step(`Navigate to ${nav.name}`, async () => {
          await page.click(`a[href="${nav.href}"]`);
          await waitForPageLoad(page);
          
          // Check URL
          expect(page.url()).toContain(nav.href);
          
          // Check if page loaded correctly
          await expect(page.locator('header')).toBeVisible();
          
          // Check for page content
          const pageContent = await page.textContent('body');
          expect(pageContent).toContain(nav.expectedText);
        });
      }
    });
    
    test('should show active states correctly', async ({ page }) => {
      const pages = ['/', '/memories', '/evolution', '/settings'];
      
      for (const href of pages) {
        await test.step(`Check active state for ${href}`, async () => {
          await page.goto(BASE_URL + href);
          await waitForPageLoad(page);
          
          // Check active button styling
          const activeButton = page.locator(`a[href="${href}"] button`);
          await expect(activeButton).toHaveClass(/bg-zinc-800/);
          
          // Check that other buttons are not active
          const otherPages = pages.filter(p => p !== href);
          for (const otherHref of otherPages) {
            const inactiveButton = page.locator(`a[href="${otherHref}"] button`);
            await expect(inactiveButton).not.toHaveClass(/bg-zinc-800/);
          }
        });
      }
    });
    
    test('should work with keyboard navigation', async ({ page }) => {
      await test.step('Keyboard navigation through menu', async () => {
        // Focus on first navigation item
        await page.keyboard.press('Tab');
        
        // Navigate through menu items
        const menuItems = ['/', '/memories', '/evolution', '/settings'];
        for (const href of menuItems) {
          const link = page.locator(`a[href="${href}"]`);
          await expect(link).toBeFocused();
          await page.keyboard.press('Enter');
          await waitForPageLoad(page);
          expect(page.url()).toContain(href);
          await page.keyboard.press('Tab');
        }
      });
    });
    
  });

  test.describe('Responsive Navigation', () => {
    
    test('should work on mobile screen sizes', async ({ page }) => {
      await test.step('Test mobile navigation', async () => {
        await page.setViewportSize({ width: 375, height: 667 });
        await page.reload();
        await waitForPageLoad(page);
        
        // Check that navigation is still visible
        await expect(page.locator('header')).toBeVisible();
        
        // Check that navigation items are present
        const navItems = [
          { href: '/', icon: 'HiHome' },
          { href: '/memories', icon: 'HiMiniRectangleStack' },
          { href: '/evolution', icon: 'TrendingUp' },
          { href: '/settings', icon: 'Settings' }
        ];
        
        for (const item of navItems) {
          const navButton = page.locator(`a[href="${item.href}"] button`);
          await expect(navButton).toBeVisible();
          
          // Check that text is hidden on mobile (should only show icons)
          const buttonText = page.locator(`a[href="${item.href}"] button span:not(.sr-only)`);
          const isTextHidden = await buttonText.isHidden();
          expect(isTextHidden).toBe(true);
        }
      });
    });
    
    test('should work on tablet screen sizes', async ({ page }) => {
      await test.step('Test tablet navigation', async () => {
        await page.setViewportSize({ width: 768, height: 1024 });
        await page.reload();
        await waitForPageLoad(page);
        
        // Check that navigation works on tablet
        await expect(page.locator('header')).toBeVisible();
        
        // Navigation should be functional
        await page.click('a[href="/memories"]');
        await waitForPageLoad(page);
        expect(page.url()).toContain('/memories');
      });
    });
    
    test('should work on desktop screen sizes', async ({ page }) => {
      await test.step('Test desktop navigation', async () => {
        await page.setViewportSize({ width: 1920, height: 1080 });
        await page.reload();
        await waitForPageLoad(page);
        
        // Check that navigation works on desktop
        await expect(page.locator('header')).toBeVisible();
        
        // Check that text is visible on desktop
        const dashboardText = page.locator('a[href="/"] button span').filter({ hasText: 'Dashboard' });
        await expect(dashboardText).toBeVisible();
      });
    });
    
  });

  test.describe('Common UI Components', () => {
    
    test('should test theme toggle functionality', async ({ page }) => {
      await test.step('Test theme toggle', async () => {
        const themeToggle = page.locator('button:has(svg)').filter({ hasText: 'Toggle theme' });
        await expect(themeToggle).toBeVisible();
        
        // Get initial theme
        const initialTheme = await page.evaluate(() => {
          return document.documentElement.getAttribute('data-theme') || 
                 (document.documentElement.classList.contains('dark') ? 'dark' : 'light');
        });
        
        // Toggle theme
        await themeToggle.click();
        await page.waitForTimeout(500);
        
        // Check that theme changed
        const newTheme = await page.evaluate(() => {
          return document.documentElement.getAttribute('data-theme') || 
                 (document.documentElement.classList.contains('dark') ? 'dark' : 'light');
        });
        
        expect(newTheme).not.toBe(initialTheme);
      });
    });
    
    test('should test user selector component', async ({ page }) => {
      await test.step('Test user selector', async () => {
        const userSelector = page.locator('[data-testid="user-selector"]').or(
          page.locator('button:has-text("Aung"), button:has-text("Yohanna")')
        );
        
        if (await userSelector.count() > 0) {
          await expect(userSelector.first()).toBeVisible();
          await userSelector.first().click();
          await page.waitForTimeout(500);
        }
      });
    });
    
    test('should test refresh button functionality', async ({ page }) => {
      await test.step('Test refresh button', async () => {
        const refreshButton = page.locator('button:has-text("Refresh")').or(
          page.locator('button:has(svg)')
        );
        
        await expect(refreshButton).toBeVisible();
        await refreshButton.click();
        await page.waitForTimeout(1000);
        
        // Check that page is still functional after refresh
        await expect(page.locator('header')).toBeVisible();
      });
    });
    
    test('should test create memory dialog', async ({ page }) => {
      await test.step('Test create memory dialog', async () => {
        const createButton = page.locator('button:has-text("Create Memory")').or(
          page.locator('button:has-text("Add Memory")')
        );
        
        if (await createButton.count() > 0) {
          await createButton.click();
          await page.waitForTimeout(500);
          
          // Check if modal/dialog opened
          const dialog = page.locator('[role="dialog"]').or(page.locator('.modal'));
          if (await dialog.count() > 0) {
            await expect(dialog).toBeVisible();
            
            // Close dialog
            const closeButton = page.locator('button:has-text("Cancel")').or(
              page.locator('button[aria-label="Close"]')
            );
            if (await closeButton.count() > 0) {
              await closeButton.click();
            } else {
              await page.keyboard.press('Escape');
            }
          }
        }
      });
    });
    
  });

  test.describe('Page-Specific Components', () => {
    
    test('should test memories page components', async ({ page }) => {
      await test.step('Test memories page', async () => {
        await page.goto(BASE_URL + '/memories');
        await waitForPageLoad(page);
        
        // Check for memory table/list
        const memoryTable = page.locator('table').or(page.locator('[data-testid="memory-list"]'));
        if (await memoryTable.count() > 0) {
          await expect(memoryTable).toBeVisible();
        }
        
        // Check for filters
        const filters = page.locator('input[placeholder*="Search"]').or(
          page.locator('button:has-text("Filter")')
        );
        if (await filters.count() > 0) {
          await expect(filters.first()).toBeVisible();
        }
        
        // Check for pagination
        const pagination = page.locator('[aria-label="Pagination"]').or(
          page.locator('button:has-text("Previous"), button:has-text("Next")')
        );
        if (await pagination.count() > 0) {
          await expect(pagination.first()).toBeVisible();
        }
      });
    });
    
    test('should test evolution page components', async ({ page }) => {
      await test.step('Test evolution page', async () => {
        await page.goto(BASE_URL + '/evolution');
        await waitForPageLoad(page);
        
        // Check for evolution dashboard components
        const evolutionContent = page.locator('main').or(page.locator('.evolution-dashboard'));
        await expect(evolutionContent).toBeVisible();
        
        // Look for charts or metrics
        const charts = page.locator('canvas').or(page.locator('[data-testid="chart"]'));
        if (await charts.count() > 0) {
          await expect(charts.first()).toBeVisible();
        }
      });
    });
    
    test('should test settings page components', async ({ page }) => {
      await test.step('Test settings page', async () => {
        await page.goto(BASE_URL + '/settings');
        await waitForPageLoad(page);
        
        // Check for settings content
        const settingsContent = page.locator('main').or(page.locator('.settings'));
        await expect(settingsContent).toBeVisible();
        
        // Look for form elements
        const formElements = page.locator('input, select, textarea, button');
        if (await formElements.count() > 0) {
          await expect(formElements.first()).toBeVisible();
        }
      });
    });
    
  });

  test.describe('Loading States and Skeletons', () => {
    
    test('should test loading states', async ({ page }) => {
      await test.step('Test loading states', async () => {
        // Navigate to a page and look for loading indicators
        await page.goto(BASE_URL + '/memories');
        
        // Look for loading skeletons during initial load
        const loadingSkeletons = page.locator('.animate-pulse').or(
          page.locator('[data-testid="loading"]')
        );
        
        // Allow some time for loading states to appear and disappear
        await page.waitForTimeout(2000);
        
        // Check that content eventually loads
        await expect(page.locator('main')).toBeVisible();
      });
    });
    
  });

  test.describe('Error Handling', () => {
    
    test('should handle 404 pages correctly', async ({ page }) => {
      await test.step('Test 404 page', async () => {
        await page.goto(BASE_URL + '/nonexistent-page');
        await waitForPageLoad(page);
        
        // Check for 404 page content
        const notFoundContent = page.locator('text="404"').or(
          page.locator('text="Page Not Found"')
        );
        await expect(notFoundContent).toBeVisible();
        
        // Check for navigation back to home
        const homeButton = page.locator('a[href="/"]').or(
          page.locator('button:has-text("Home")')
        );
        if (await homeButton.count() > 0) {
          await expect(homeButton).toBeVisible();
        }
      });
    });
    
    test('should handle network errors gracefully', async ({ page }) => {
      await test.step('Test network error handling', async () => {
        // Block network requests to simulate network error
        await page.route('**/api/**', route => {
          route.abort();
        });
        
        await page.goto(BASE_URL);
        await waitForPageLoad(page);
        
        // Check that page still loads basic structure
        await expect(page.locator('header')).toBeVisible();
        
        // Look for error messages or fallback content
        const errorMessages = page.locator('text="Error"').or(
          page.locator('text="Failed to load"')
        );
        
        // Page should still be functional even with API errors
        await expect(page.locator('main')).toBeVisible();
      });
    });
    
  });

  test.describe('Accessibility Testing', () => {
    
    test('should test keyboard navigation across all pages', async ({ page }) => {
      const pages = ['/', '/memories', '/evolution', '/settings'];
      
      for (const href of pages) {
        await test.step(`Test keyboard navigation on ${href}`, async () => {
          await page.goto(BASE_URL + href);
          await waitForPageLoad(page);
          
          // Test Tab navigation
          await page.keyboard.press('Tab');
          await page.waitForTimeout(100);
          
          // Check that focus is visible
          const focusedElement = page.locator(':focus');
          await expect(focusedElement).toBeVisible();
          
          // Test that we can navigate through interactive elements
          for (let i = 0; i < 10; i++) {
            await page.keyboard.press('Tab');
            await page.waitForTimeout(50);
          }
        });
      }
    });
    
    test('should test screen reader compatibility', async ({ page }) => {
      await test.step('Test ARIA labels and roles', async () => {
        await page.goto(BASE_URL);
        await waitForPageLoad(page);
        
        // Check for proper ARIA labels
        const interactiveElements = page.locator('button, a, input, select');
        const count = await interactiveElements.count();
        
        for (let i = 0; i < Math.min(count, 20); i++) {
          const element = interactiveElements.nth(i);
          const ariaLabel = await element.getAttribute('aria-label');
          const text = await element.textContent();
          
          // Each interactive element should have either aria-label or visible text
          expect(ariaLabel || text?.trim()).toBeTruthy();
        }
      });
    });
    
  });

  test.describe('Console and JavaScript Errors', () => {
    
    test('should check for console errors on all pages', async ({ page }) => {
      const pages = ['/', '/memories', '/evolution', '/settings'];
      const consoleErrors = [];
      
      page.on('console', (msg) => {
        if (msg.type() === 'error') {
          consoleErrors.push(msg.text());
        }
      });
      
      for (const href of pages) {
        await test.step(`Check console errors on ${href}`, async () => {
          await page.goto(BASE_URL + href);
          await waitForPageLoad(page);
          
          // Wait a bit more for any delayed errors
          await page.waitForTimeout(2000);
        });
      }
      
      // Report console errors but don't fail the test for minor issues
      if (consoleErrors.length > 0) {
        console.log('Console errors found:', consoleErrors);
      }
    });
    
  });

  test.describe('Performance and UX', () => {
    
    test('should test page load performance', async ({ page }) => {
      const pages = ['/', '/memories', '/evolution', '/settings'];
      
      for (const href of pages) {
        await test.step(`Test load performance for ${href}`, async () => {
          const startTime = Date.now();
          await page.goto(BASE_URL + href);
          await waitForPageLoad(page);
          const loadTime = Date.now() - startTime;
          
          // Page should load within reasonable time (10 seconds)
          expect(loadTime).toBeLessThan(10000);
          
          // Check that page is interactive
          await expect(page.locator('header')).toBeVisible();
        });
      }
    });
    
    test('should test responsive design breakpoints', async ({ page }) => {
      const breakpoints = [
        { width: 375, height: 667, name: 'Mobile' },
        { width: 768, height: 1024, name: 'Tablet' },
        { width: 1024, height: 768, name: 'Desktop Small' },
        { width: 1920, height: 1080, name: 'Desktop Large' }
      ];
      
      for (const breakpoint of breakpoints) {
        await test.step(`Test responsive design at ${breakpoint.name}`, async () => {
          await page.setViewportSize({ width: breakpoint.width, height: breakpoint.height });
          await page.reload();
          await waitForPageLoad(page);
          
          // Check that header is visible and functional
          await expect(page.locator('header')).toBeVisible();
          
          // Check that navigation works
          await page.click('a[href="/memories"]');
          await waitForPageLoad(page);
          expect(page.url()).toContain('/memories');
          
          // Check that content doesn't overflow
          const body = page.locator('body');
          const overflowX = await body.evaluate(el => window.getComputedStyle(el).overflowX);
          expect(overflowX).not.toBe('scroll');
        });
      }
    });
    
  });

});