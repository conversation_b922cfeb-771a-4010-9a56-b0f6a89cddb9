const { chromium } = require('playwright');

async function testApplication() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  const testResults = [];
  
  // Test home page
  console.log('🏠 Testing Home Page...');
  try {
    await page.goto('http://localhost:3000');
    await page.waitForSelector('h1', { timeout: 10000 });
    const title = await page.textContent('h1');
    console.log(`✅ Home page loaded successfully. Title: ${title}`);
    testResults.push({ page: 'Home', status: 'PASS', title });
  } catch (error) {
    console.log(`❌ Home page failed: ${error.message}`);
    testResults.push({ page: 'Home', status: 'FAIL', error: error.message });
  }
  
  // Test memories page
  console.log('🧠 Testing Memories Page...');
  try {
    await page.goto('http://localhost:3000/memories');
    await page.waitForSelector('h1', { timeout: 10000 });
    const title = await page.textContent('h1');
    console.log(`✅ Memories page loaded successfully. Title: ${title}`);
    testResults.push({ page: 'Memories', status: 'PASS', title });
  } catch (error) {
    console.log(`❌ Memories page failed: ${error.message}`);
    testResults.push({ page: 'Memories', status: 'FAIL', error: error.message });
  }
  
  // Test evolution page
  console.log('🧬 Testing Evolution Page...');
  try {
    await page.goto('http://localhost:3000/evolution');
    await page.waitForSelector('h1', { timeout: 10000 });
    const title = await page.textContent('h1');
    console.log(`✅ Evolution page loaded successfully. Title: ${title}`);
    
    // Check for simplified status cards
    const statusCards = await page.$$('.card');
    console.log(`Found ${statusCards.length} status cards`);
    
    // Check for specific simplified elements
    const systemStatus = await page.$('text=System Status');
    const todayOps = await page.$('text=Today\'s Operations');
    const totalOps = await page.$('text=Total Operations');
    
    if (systemStatus && todayOps && totalOps) {
      console.log('✅ Simplified evolution dashboard elements found');
      testResults.push({ page: 'Evolution', status: 'PASS', title, cards: statusCards.length });
    } else {
      console.log('⚠️ Some simplified elements missing');
      testResults.push({ page: 'Evolution', status: 'PARTIAL', title, cards: statusCards.length });
    }
  } catch (error) {
    console.log(`❌ Evolution page failed: ${error.message}`);
    testResults.push({ page: 'Evolution', status: 'FAIL', error: error.message });
  }
  
  // Test settings page
  console.log('⚙️ Testing Settings Page...');
  try {
    await page.goto('http://localhost:3000/settings');
    await page.waitForSelector('h1', { timeout: 10000 });
    const title = await page.textContent('h1');
    console.log(`✅ Settings page loaded successfully. Title: ${title}`);
    testResults.push({ page: 'Settings', status: 'PASS', title });
  } catch (error) {
    console.log(`❌ Settings page failed: ${error.message}`);
    testResults.push({ page: 'Settings', status: 'FAIL', error: error.message });
  }
  
  // Test evolution settings page
  console.log('🧬⚙️ Testing Evolution Settings Page...');
  try {
    await page.goto('http://localhost:3000/settings/evolution');
    await page.waitForSelector('h1', { timeout: 10000 });
    const title = await page.textContent('h1');
    console.log(`✅ Evolution settings page loaded successfully. Title: ${title}`);
    testResults.push({ page: 'Evolution Settings', status: 'PASS', title });
  } catch (error) {
    console.log(`❌ Evolution settings page failed: ${error.message}`);
    testResults.push({ page: 'Evolution Settings', status: 'FAIL', error: error.message });
  }
  
  // Test navigation
  console.log('🧭 Testing Navigation...');
  try {
    await page.goto('http://localhost:3000');
    
    // Check for navigation elements
    const navItems = await page.$$('nav a, [role="navigation"] a');
    console.log(`Found ${navItems.length} navigation items`);
    
    // Test clicking navigation items
    for (let i = 0; i < Math.min(navItems.length, 5); i++) {
      try {
        const navText = await navItems[i].textContent();
        await navItems[i].click();
        await page.waitForLoadState('networkidle', { timeout: 5000 });
        console.log(`✅ Navigation to "${navText}" successful`);
      } catch (navError) {
        console.log(`⚠️ Navigation item ${i} failed: ${navError.message}`);
      }
    }
    
    testResults.push({ page: 'Navigation', status: 'PASS', items: navItems.length });
  } catch (error) {
    console.log(`❌ Navigation test failed: ${error.message}`);
    testResults.push({ page: 'Navigation', status: 'FAIL', error: error.message });
  }
  
  // Test API connectivity
  console.log('🔗 Testing API Connectivity...');
  try {
    const response = await page.goto('http://localhost:8765/health');
    const healthData = await response.json();
    console.log(`✅ API health check successful: ${JSON.stringify(healthData)}`);
    testResults.push({ page: 'API Health', status: 'PASS', data: healthData });
  } catch (error) {
    console.log(`❌ API connectivity failed: ${error.message}`);
    testResults.push({ page: 'API Health', status: 'FAIL', error: error.message });
  }
  
  // Test evolution API
  console.log('🧬🔗 Testing Evolution API...');
  try {
    const response = await page.goto('http://localhost:8765/api/v1/evolution/status');
    const evolutionData = await response.json();
    console.log(`✅ Evolution API successful: ${JSON.stringify(evolutionData)}`);
    testResults.push({ page: 'Evolution API', status: 'PASS', data: evolutionData });
  } catch (error) {
    console.log(`❌ Evolution API failed: ${error.message}`);
    testResults.push({ page: 'Evolution API', status: 'FAIL', error: error.message });
  }
  
  // Test memory creation flow
  console.log('📝 Testing Memory Creation...');
  try {
    await page.goto('http://localhost:3000/memories');
    await page.waitForSelector('button', { timeout: 10000 });
    
    // Look for create memory button
    const createBtn = await page.$('button:has-text("Create Memory"), button:has-text("Add Memory"), button:has-text("New Memory")');
    
    if (createBtn) {
      await createBtn.click();
      await page.waitForSelector('form, [role="dialog"]', { timeout: 5000 });
      console.log('✅ Memory creation dialog/form opened successfully');
      testResults.push({ page: 'Memory Creation', status: 'PASS' });
    } else {
      console.log('⚠️ Create memory button not found');
      testResults.push({ page: 'Memory Creation', status: 'PARTIAL', note: 'Create button not found' });
    }
  } catch (error) {
    console.log(`❌ Memory creation test failed: ${error.message}`);
    testResults.push({ page: 'Memory Creation', status: 'FAIL', error: error.message });
  }
  
  // Print summary
  console.log('\n📊 TEST SUMMARY:');
  console.log('================');
  const passCount = testResults.filter(r => r.status === 'PASS').length;
  const partialCount = testResults.filter(r => r.status === 'PARTIAL').length;
  const failCount = testResults.filter(r => r.status === 'FAIL').length;
  
  console.log(`✅ PASSED: ${passCount}`);
  console.log(`⚠️ PARTIAL: ${partialCount}`);
  console.log(`❌ FAILED: ${failCount}`);
  
  testResults.forEach(result => {
    const status = result.status === 'PASS' ? '✅' : result.status === 'PARTIAL' ? '⚠️' : '❌';
    console.log(`${status} ${result.page}: ${result.title || result.error || 'N/A'}`);
  });
  
  await browser.close();
  return testResults;
}

testApplication().catch(console.error);