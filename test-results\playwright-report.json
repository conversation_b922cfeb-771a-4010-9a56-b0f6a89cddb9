{"config": {"configFile": "/mnt/d/_<PERSON>-<PERSON>/memory-master-evolveui/memory-master-v2/playwright.config.js", "rootDir": "/mnt/d/_<PERSON>-<PERSON>/memory-master-evolveui/memory-master-v2", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/playwright-report.json"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/mnt/d/_<PERSON>-<PERSON>/memory-master-evolveui/memory-master-v2/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/mnt/d/_<PERSON>-<PERSON>/memory-master-evolveui/memory-master-v2", "testIgnore": [], "testMatch": ["evolution-comprehensive-test.js"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.2", "workers": 1, "webServer": null}, "suites": [], "errors": [{"message": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments.", "stack": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments."}], "stats": {"startTime": "2025-07-04T13:25:55.959Z", "duration": 389.58100000000013, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}