const { chromium } = require('playwright');

async function takeScreenshot() {
  console.log('🚀 Taking screenshot of memories page...');
  
  const browser = await chromium.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
  });
  
  const page = await browser.newPage();
  
  try {
    // Navigate to the memories page
    console.log('📄 Navigating to http://localhost:3000/memories...');
    await page.goto('http://localhost:3000/memories', { 
      waitUntil: 'networkidle',
      timeout: 30000
    });
    
    // Wait for the page to fully load
    await page.waitForTimeout(5000);
    
    // Take screenshot
    console.log('📸 Taking screenshot...');
    await page.screenshot({ 
      path: '/tmp/memories-page.png',
      fullPage: true
    });
    
    console.log('✅ Screenshot saved to /tmp/memories-page.png');
    
    // Get page title
    const title = await page.title();
    console.log(`📄 Page title: ${title}`);
    
    // Get page URL
    const url = await page.url();
    console.log(`🔗 Page URL: ${url}`);
    
    // Count elements
    const buttons = await page.locator('button').count();
    console.log(`🔘 Found ${buttons} buttons`);
    
    const inputs = await page.locator('input').count();
    console.log(`📝 Found ${inputs} inputs`);
    
    const tables = await page.locator('table').count();
    console.log(`📊 Found ${tables} tables`);
    
    // Check for specific elements
    const createButton = await page.locator('button').filter({ hasText: /create/i }).first();
    const createButtonExists = await createButton.isVisible();
    console.log(`🆕 Create button exists: ${createButtonExists}`);
    
    if (createButtonExists) {
      const createButtonText = await createButton.textContent();
      console.log(`🆕 Create button text: "${createButtonText}"`);
    }
    
    // Check for loading states
    const loadingElements = await page.locator('[data-testid="loading"], .loading, .spinner').count();
    console.log(`⏳ Found ${loadingElements} loading elements`);
    
    // Check for error states
    const errorElements = await page.locator('[data-testid="error"], .error, .alert-destructive').count();
    console.log(`❌ Found ${errorElements} error elements`);
    
    // Take a second screenshot after waiting more
    await page.waitForTimeout(3000);
    await page.screenshot({ 
      path: '/tmp/memories-page-final.png',
      fullPage: true
    });
    
    console.log('✅ Final screenshot saved to /tmp/memories-page-final.png');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
    // Take error screenshot
    try {
      await page.screenshot({ 
        path: '/tmp/memories-page-error.png',
        fullPage: true
      });
      console.log('📸 Error screenshot saved to /tmp/memories-page-error.png');
    } catch (screenshotError) {
      console.error('❌ Could not take error screenshot:', screenshotError.message);
    }
  } finally {
    await browser.close();
  }
}

takeScreenshot().catch(console.error);