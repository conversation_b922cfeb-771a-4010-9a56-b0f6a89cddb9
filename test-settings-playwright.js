const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function testSettingsPages() {
  console.log('Starting Settings Pages Test with Playwright...\n');
  
  // Create screenshots directory
  const screenshotsDir = path.join(__dirname, 'test-screenshots');
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir, { recursive: true });
  }

  let browser;
  let page;
  const testResults = {
    passed: 0,
    failed: 0,
    errors: [],
    screenshots: [],
    findings: []
  };

  try {
    // Launch browser
    browser = await chromium.launch({ 
      headless: false, // Set to true for headless mode
      slowMo: 500 // Slow down actions for better visibility
    });
    
    const context = await browser.newContext({
      viewport: { width: 1920, height: 1080 }
    });
    
    page = await context.newPage();

    // Enable console logging
    page.on('console', msg => {
      console.log(`Browser Console [${msg.type()}]: ${msg.text()}`);
    });

    // Enable error logging
    page.on('pageerror', error => {
      console.error(`Browser Error: ${error.message}`);
      testResults.errors.push(`Page Error: ${error.message}`);
    });

    // Test 1: Navigate to settings page
    console.log('🔍 Test 1: Navigating to /settings page...');
    try {
      await page.goto('http://localhost:3000/settings', { 
        waitUntil: 'networkidle',
        timeout: 30000 
      });
      
      // Wait for page to load
      await page.waitForSelector('body', { timeout: 10000 });
      
      // Take screenshot
      const screenshotPath = path.join(screenshotsDir, '01-settings-main.png');
      await page.screenshot({ path: screenshotPath, fullPage: true });
      testResults.screenshots.push(screenshotPath);
      
      console.log('✅ Successfully navigated to /settings');
      testResults.passed++;
    } catch (error) {
      console.error('❌ Failed to navigate to /settings:', error.message);
      testResults.failed++;
      testResults.errors.push(`Navigation Error: ${error.message}`);
    }

    // Test 2: Verify page loads correctly
    console.log('\n🔍 Test 2: Verifying page loads correctly...');
    try {
      // Check for main page elements
      const title = await page.title();
      console.log(`Page Title: ${title}`);
      
      // Check for settings content
      const settingsContent = await page.locator('h1, h2, [data-testid="settings"], .settings').first();
      if (await settingsContent.isVisible()) {
        console.log('✅ Settings page content is visible');
        testResults.passed++;
      } else {
        console.log('❌ Settings page content not found');
        testResults.failed++;
        testResults.errors.push('Settings page content not visible');
      }
      
      // Check for any loading states
      const loadingElements = await page.locator('[data-testid="loading"], .loading, .spinner').count();
      if (loadingElements === 0) {
        console.log('✅ No loading indicators found - page fully loaded');
        testResults.findings.push('Page loaded without loading indicators');
      } else {
        console.log(`⚠️  Found ${loadingElements} loading indicators`);
        testResults.findings.push(`Found ${loadingElements} loading indicators`);
      }
      
    } catch (error) {
      console.error('❌ Page load verification failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Page Load Error: ${error.message}`);
    }

    // Test 3: Test settings navigation/tabs
    console.log('\n🔍 Test 3: Testing settings navigation/tabs...');
    try {
      // Look for navigation elements
      const navElements = await page.locator('nav, .nav, [role="navigation"], .tabs, .tab-list').all();
      console.log(`Found ${navElements.length} navigation elements`);
      
      // Look for evolution settings link
      const evolutionLink = await page.locator('a[href="/settings/evolution"], a[href*="evolution"]').first();
      if (await evolutionLink.isVisible()) {
        console.log('✅ Evolution settings link found');
        testResults.passed++;
      } else {
        console.log('❌ Evolution settings link not found');
        testResults.failed++;
        testResults.errors.push('Evolution settings link not found');
      }
      
      // Test navigation to evolution settings
      if (await evolutionLink.isVisible()) {
        await evolutionLink.click();
        await page.waitForTimeout(2000);
        
        const currentUrl = page.url();
        console.log(`Current URL after clicking evolution link: ${currentUrl}`);
        
        if (currentUrl.includes('/settings/evolution')) {
          console.log('✅ Successfully navigated to evolution settings');
          testResults.passed++;
        } else {
          console.log('❌ Failed to navigate to evolution settings');
          testResults.failed++;
          testResults.errors.push('Evolution settings navigation failed');
        }
      }
      
    } catch (error) {
      console.error('❌ Navigation test failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Navigation Error: ${error.message}`);
    }

    // Test 4: Test evolution settings tabs
    console.log('\n🔍 Test 4: Testing evolution settings tabs...');
    try {
      // Navigate to evolution settings if not already there
      if (!page.url().includes('/settings/evolution')) {
        await page.goto('http://localhost:3000/settings/evolution', { 
          waitUntil: 'networkidle',
          timeout: 30000 
        });
      }
      
      // Take screenshot of evolution settings
      const evolutionScreenshot = path.join(screenshotsDir, '02-settings-evolution.png');
      await page.screenshot({ path: evolutionScreenshot, fullPage: true });
      testResults.screenshots.push(evolutionScreenshot);
      
      // Test each tab
      const tabs = ['Overview', 'Domain', 'Prompts', 'Testing', 'Advanced'];
      
      for (const tabName of tabs) {
        console.log(`\n  Testing ${tabName} tab...`);
        try {
          // Look for tab button
          const tabButton = await page.locator(`[data-testid="${tabName.toLowerCase()}-tab"], button:has-text("${tabName}"), .tab:has-text("${tabName}")`).first();
          
          if (await tabButton.isVisible()) {
            console.log(`  ✅ ${tabName} tab found`);
            
            // Click the tab
            await tabButton.click();
            await page.waitForTimeout(1000);
            
            // Take screenshot of tab content
            const tabScreenshot = path.join(screenshotsDir, `03-evolution-${tabName.toLowerCase()}-tab.png`);
            await page.screenshot({ path: tabScreenshot, fullPage: true });
            testResults.screenshots.push(tabScreenshot);
            
            // Check for tab content
            const tabContent = await page.locator(`[data-testid="${tabName.toLowerCase()}-content"], .tab-content`).first();
            if (await tabContent.isVisible()) {
              console.log(`  ✅ ${tabName} tab content loaded`);
              testResults.passed++;
            } else {
              console.log(`  ⚠️  ${tabName} tab content not clearly visible`);
              testResults.findings.push(`${tabName} tab content visibility unclear`);
            }
            
          } else {
            console.log(`  ❌ ${tabName} tab not found`);
            testResults.failed++;
            testResults.errors.push(`${tabName} tab not found`);
          }
          
        } catch (error) {
          console.error(`  ❌ ${tabName} tab test failed:`, error.message);
          testResults.failed++;
          testResults.errors.push(`${tabName} tab error: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.error('❌ Evolution settings tabs test failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Evolution tabs error: ${error.message}`);
    }

    // Test 5: Test configuration forms and inputs
    console.log('\n🔍 Test 5: Testing configuration forms and inputs...');
    try {
      // Look for form elements
      const forms = await page.locator('form').all();
      console.log(`Found ${forms.length} forms`);
      
      const inputs = await page.locator('input, textarea, select').all();
      console.log(`Found ${inputs.length} input elements`);
      
      const buttons = await page.locator('button').all();
      console.log(`Found ${buttons.length} buttons`);
      
      // Test some common form interactions
      const textInputs = await page.locator('input[type="text"], input[type="email"], input[type="password"], textarea').all();
      
      for (let i = 0; i < Math.min(3, textInputs.length); i++) {
        try {
          const input = textInputs[i];
          if (await input.isVisible() && await input.isEnabled()) {
            const placeholder = await input.getAttribute('placeholder') || `test-input-${i}`;
            console.log(`  Testing input with placeholder: ${placeholder}`);
            
            await input.fill('test-value');
            await page.waitForTimeout(500);
            
            const value = await input.inputValue();
            if (value === 'test-value') {
              console.log(`  ✅ Input ${i} accepts text correctly`);
              testResults.passed++;
            } else {
              console.log(`  ❌ Input ${i} did not accept text correctly`);
              testResults.failed++;
            }
            
            // Clear the input
            await input.clear();
          }
        } catch (error) {
          console.error(`  ❌ Input ${i} test failed:`, error.message);
          testResults.errors.push(`Input ${i} error: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.error('❌ Forms and inputs test failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Forms test error: ${error.message}`);
    }

    // Test 6: Test save/update functionality
    console.log('\n🔍 Test 6: Testing save/update functionality...');
    try {
      // Look for save/update buttons
      const saveButtons = await page.locator('button:has-text("Save"), button:has-text("Update"), button[type="submit"]').all();
      console.log(`Found ${saveButtons.length} save/update buttons`);
      
      for (let i = 0; i < Math.min(2, saveButtons.length); i++) {
        try {
          const button = saveButtons[i];
          if (await button.isVisible() && await button.isEnabled()) {
            const buttonText = await button.textContent();
            console.log(`  Testing save button: ${buttonText}`);
            
            // Click the button (but be careful not to actually save invalid data)
            await button.click();
            await page.waitForTimeout(2000);
            
            // Look for success/error messages
            const successMessage = await page.locator('.success, .alert-success, [data-testid="success"]').first();
            const errorMessage = await page.locator('.error, .alert-error, [data-testid="error"]').first();
            
            if (await successMessage.isVisible()) {
              console.log(`  ✅ Save button ${i} showed success message`);
              testResults.passed++;
            } else if (await errorMessage.isVisible()) {
              console.log(`  ⚠️  Save button ${i} showed error message (expected for test data)`);
              testResults.findings.push(`Save button ${i} validation working`);
            } else {
              console.log(`  ⚠️  Save button ${i} - no clear feedback message`);
              testResults.findings.push(`Save button ${i} - unclear feedback`);
            }
          }
        } catch (error) {
          console.error(`  ❌ Save button ${i} test failed:`, error.message);
          testResults.errors.push(`Save button ${i} error: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.error('❌ Save/update functionality test failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Save/update test error: ${error.message}`);
    }

    // Test 7: Test system status dialogs and modals
    console.log('\n🔍 Test 7: Testing system status dialogs and modals...');
    try {
      // Look for buttons that might open dialogs/modals
      const modalButtons = await page.locator('button:has-text("Status"), button:has-text("Dialog"), button:has-text("Modal"), button:has-text("System"), button:has-text("Report")').all();
      console.log(`Found ${modalButtons.length} potential modal/dialog buttons`);
      
      for (let i = 0; i < Math.min(3, modalButtons.length); i++) {
        try {
          const button = modalButtons[i];
          if (await button.isVisible() && await button.isEnabled()) {
            const buttonText = await button.textContent();
            console.log(`  Testing modal button: ${buttonText}`);
            
            await button.click();
            await page.waitForTimeout(1500);
            
            // Look for modal/dialog elements
            const modal = await page.locator('[role="dialog"], .modal, .dialog').first();
            if (await modal.isVisible()) {
              console.log(`  ✅ Modal/dialog opened successfully`);
              testResults.passed++;
              
              // Take screenshot of modal
              const modalScreenshot = path.join(screenshotsDir, `04-modal-${i}.png`);
              await page.screenshot({ path: modalScreenshot, fullPage: true });
              testResults.screenshots.push(modalScreenshot);
              
              // Try to close modal
              const closeButton = await page.locator('[data-testid="close"], button:has-text("Close"), button:has-text("×"), .modal-close').first();
              if (await closeButton.isVisible()) {
                await closeButton.click();
                await page.waitForTimeout(1000);
                console.log(`  ✅ Modal closed successfully`);
                testResults.passed++;
              } else {
                // Try pressing Escape
                await page.keyboard.press('Escape');
                await page.waitForTimeout(1000);
                console.log(`  ✅ Modal closed with Escape key`);
                testResults.passed++;
              }
            } else {
              console.log(`  ❌ Modal/dialog did not open`);
              testResults.failed++;
            }
          }
        } catch (error) {
          console.error(`  ❌ Modal button ${i} test failed:`, error.message);
          testResults.errors.push(`Modal button ${i} error: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.error('❌ System status dialogs test failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Dialogs test error: ${error.message}`);
    }

    // Test 8: Check for proper validation and error handling
    console.log('\n🔍 Test 8: Testing validation and error handling...');
    try {
      // Test form validation by submitting empty/invalid forms
      const forms = await page.locator('form').all();
      
      for (let i = 0; i < Math.min(2, forms.length); i++) {
        try {
          const form = forms[i];
          console.log(`  Testing form ${i} validation...`);
          
          // Try to submit form without filling required fields
          const submitButton = await form.locator('button[type="submit"], input[type="submit"]').first();
          if (await submitButton.isVisible() && await submitButton.isEnabled()) {
            await submitButton.click();
            await page.waitForTimeout(1000);
            
            // Look for validation messages
            const validationMessages = await page.locator('.error, .invalid, .validation-error, [data-testid="validation-error"]').all();
            if (validationMessages.length > 0) {
              console.log(`  ✅ Form ${i} shows validation errors (${validationMessages.length} found)`);
              testResults.passed++;
            } else {
              console.log(`  ⚠️  Form ${i} validation unclear`);
              testResults.findings.push(`Form ${i} validation not clearly visible`);
            }
          }
        } catch (error) {
          console.error(`  ❌ Form ${i} validation test failed:`, error.message);
          testResults.errors.push(`Form ${i} validation error: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.error('❌ Validation and error handling test failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Validation test error: ${error.message}`);
    }

    // Test 9: Final verification of all settings components
    console.log('\n🔍 Test 9: Final verification of all settings components...');
    try {
      // Take final screenshot
      const finalScreenshot = path.join(screenshotsDir, '05-final-state.png');
      await page.screenshot({ path: finalScreenshot, fullPage: true });
      testResults.screenshots.push(finalScreenshot);
      
      // Check overall page health
      const pageErrors = await page.evaluate(() => {
        const errors = [];
        if (window.console && window.console.error) {
          // This is a simple check - in a real app you might have error tracking
          errors.push('Console errors may be present - check browser console');
        }
        return errors;
      });
      
      if (pageErrors.length === 0) {
        console.log('✅ No obvious page errors detected');
        testResults.passed++;
      } else {
        console.log(`⚠️  Potential page errors: ${pageErrors.length}`);
        testResults.findings.push(`Potential page errors: ${pageErrors.join(', ')}`);
      }
      
    } catch (error) {
      console.error('❌ Final verification failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Final verification error: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Critical test failure:', error.message);
    testResults.failed++;
    testResults.errors.push(`Critical error: ${error.message}`);
  } finally {
    // Clean up
    if (page) {
      await page.close();
    }
    if (browser) {
      await browser.close();
    }
  }

  // Generate test report
  console.log('\n' + '='.repeat(80));
  console.log('📋 SETTINGS PAGES TEST REPORT');
  console.log('='.repeat(80));
  console.log(`✅ Tests Passed: ${testResults.passed}`);
  console.log(`❌ Tests Failed: ${testResults.failed}`);
  console.log(`⚠️  Findings: ${testResults.findings.length}`);
  console.log(`🚨 Errors: ${testResults.errors.length}`);
  console.log(`📸 Screenshots: ${testResults.screenshots.length}`);
  
  if (testResults.errors.length > 0) {
    console.log('\n🚨 ERRORS ENCOUNTERED:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }
  
  if (testResults.findings.length > 0) {
    console.log('\n⚠️  FINDINGS:');
    testResults.findings.forEach((finding, index) => {
      console.log(`${index + 1}. ${finding}`);
    });
  }
  
  if (testResults.screenshots.length > 0) {
    console.log('\n📸 SCREENSHOTS CAPTURED:');
    testResults.screenshots.forEach((screenshot, index) => {
      console.log(`${index + 1}. ${screenshot}`);
    });
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('Test completed successfully!');
  console.log('='.repeat(80));

  return testResults;
}

// Run the test
if (require.main === module) {
  testSettingsPages().catch(console.error);
}

module.exports = { testSettingsPages };