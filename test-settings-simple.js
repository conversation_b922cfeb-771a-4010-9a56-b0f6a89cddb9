const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function testSettingsPages() {
  console.log('🚀 Starting Settings Pages Test with Puppeteer...\n');
  
  // Create screenshots directory
  const screenshotsDir = path.join(__dirname, 'test-screenshots');
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir, { recursive: true });
  }

  let browser;
  let page;
  const testResults = {
    passed: 0,
    failed: 0,
    errors: [],
    screenshots: [],
    findings: []
  };

  try {
    // Launch browser
    browser = await puppeteer.launch({ 
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    page = await browser.newPage();

    // Enable console logging
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`❌ Browser Console Error: ${msg.text()}`);
        testResults.errors.push(`Console Error: ${msg.text()}`);
      } else if (msg.type() === 'warning') {
        console.log(`⚠️  Browser Console Warning: ${msg.text()}`);
      }
    });

    // Enable error logging
    page.on('pageerror', error => {
      console.error(`❌ Browser Page Error: ${error.message}`);
      testResults.errors.push(`Page Error: ${error.message}`);
    });

    // Test 1: Navigate to settings page
    console.log('🔍 Test 1: Navigating to /settings page...');
    try {
      await page.goto('http://localhost:3000/settings', { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      });
      
      const screenshotPath = path.join(screenshotsDir, '01-settings-main.png');
      await page.screenshot({ path: screenshotPath, fullPage: true });
      testResults.screenshots.push(screenshotPath);
      
      console.log('✅ Successfully navigated to /settings');
      testResults.passed++;
    } catch (error) {
      console.error('❌ Failed to navigate to /settings:', error.message);
      testResults.failed++;
      testResults.errors.push(`Navigation Error: ${error.message}`);
    }

    // Test 2: Verify page content
    console.log('\n🔍 Test 2: Verifying page content...');
    try {
      const title = await page.title();
      console.log(`Page Title: ${title}`);
      testResults.findings.push(`Page Title: ${title}`);
      
      // Check for main headings
      const mainHeading = await page.$eval('h1', el => el.textContent).catch(() => null);
      if (mainHeading) {
        console.log(`✅ Main heading found: "${mainHeading}"`);
        testResults.passed++;
        testResults.findings.push(`Main heading: ${mainHeading}`);
      } else {
        console.log('❌ No main heading found');
        testResults.failed++;
        testResults.errors.push('No main heading found');
      }
      
      // Check for settings-related content
      const settingsElements = await page.$$eval('*', els => 
        els.filter(el => el.textContent && el.textContent.toLowerCase().includes('settings')).length
      );
      
      console.log(`Found ${settingsElements} elements containing "settings"`);
      testResults.findings.push(`Elements with "settings": ${settingsElements}`);
      
      if (settingsElements > 0) {
        console.log('✅ Settings content found');
        testResults.passed++;
      } else {
        console.log('❌ No settings content found');
        testResults.failed++;
        testResults.errors.push('No settings content found');
      }
      
    } catch (error) {
      console.error('❌ Page content verification failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Content verification error: ${error.message}`);
    }

    // Test 3: Test form elements
    console.log('\n🔍 Test 3: Testing form elements...');
    try {
      const forms = await page.$$('form');
      console.log(`Found ${forms.length} forms`);
      testResults.findings.push(`Forms found: ${forms.length}`);
      
      const inputs = await page.$$('input, textarea, select');
      console.log(`Found ${inputs.length} input elements`);
      testResults.findings.push(`Input elements: ${inputs.length}`);
      
      const buttons = await page.$$('button');
      console.log(`Found ${buttons.length} buttons`);
      testResults.findings.push(`Buttons found: ${buttons.length}`);
      
      // Test text inputs
      const textInputs = await page.$$('input[type="text"], input[type="email"], input[type="password"], textarea');
      console.log(`Testing ${textInputs.length} text inputs...`);
      
      for (let i = 0; i < Math.min(3, textInputs.length); i++) {
        try {
          const input = textInputs[i];
          const isVisible = await input.isIntersectingViewport();
          
          if (isVisible) {
            await input.focus();
            await input.type('test-value');
            await page.waitForTimeout(500);
            
            const value = await input.evaluate(el => el.value);
            if (value === 'test-value') {
              console.log(`  ✅ Input ${i + 1} accepts text correctly`);
              testResults.passed++;
            } else {
              console.log(`  ❌ Input ${i + 1} did not accept text correctly`);
              testResults.failed++;
            }
            
            await input.evaluate(el => el.value = '');
          } else {
            console.log(`  ⚠️  Input ${i + 1} is not visible`);
            testResults.findings.push(`Input ${i + 1} not visible`);
          }
        } catch (error) {
          console.error(`  ❌ Input ${i + 1} test failed:`, error.message);
          testResults.errors.push(`Input ${i + 1} error: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.error('❌ Form elements test failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Form elements test error: ${error.message}`);
    }

    // Test 4: Test button interactions
    console.log('\n🔍 Test 4: Testing button interactions...');
    try {
      const saveButtons = await page.$$('button');
      const saveButtonsText = await Promise.all(
        saveButtons.map(btn => btn.evaluate(el => el.textContent))
      );
      
      console.log(`Button texts: ${saveButtonsText.join(', ')}`);
      testResults.findings.push(`Button texts: ${saveButtonsText.join(', ')}`);
      
      // Find and test save button
      const saveButton = await page.$('button:has-text("Save"), button[type="submit"]');
      if (!saveButton) {
        // Try alternative selectors
        const alternativeButtons = await page.$$('button');
        for (let btn of alternativeButtons) {
          const text = await btn.evaluate(el => el.textContent);
          if (text.toLowerCase().includes('save')) {
            console.log(`  ✅ Found save button: "${text}"`);
            testResults.passed++;
            break;
          }
        }
      } else {
        console.log('  ✅ Save button found');
        testResults.passed++;
      }
      
    } catch (error) {
      console.error('❌ Button interactions test failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Button interactions test error: ${error.message}`);
    }

    // Test 5: Test navigation to evolution settings
    console.log('\n🔍 Test 5: Testing navigation to evolution settings...');
    try {
      await page.goto('http://localhost:3000/settings/evolution', { 
        waitUntil: 'networkidle2',
        timeout: 10000 
      });
      
      const evolutionScreenshot = path.join(screenshotsDir, '02-evolution-settings.png');
      await page.screenshot({ path: evolutionScreenshot, fullPage: true });
      testResults.screenshots.push(evolutionScreenshot);
      
      const status = await page.evaluate(() => window.location.href);
      console.log(`Current URL: ${status}`);
      
      if (status.includes('/settings/evolution')) {
        console.log('✅ Successfully navigated to evolution settings');
        testResults.passed++;
      } else {
        console.log('❌ Failed to navigate to evolution settings');
        testResults.failed++;
        testResults.errors.push('Evolution settings navigation failed');
      }
      
    } catch (error) {
      console.error('❌ Evolution settings navigation failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Evolution settings error: ${error.message}`);
    }

    // Test 6: Test evolution page content (if accessible)
    console.log('\n🔍 Test 6: Testing evolution page content...');
    try {
      const pageContent = await page.evaluate(() => document.body.textContent);
      const hasEvolutionContent = pageContent.toLowerCase().includes('evolution');
      
      if (hasEvolutionContent) {
        console.log('✅ Evolution content found');
        testResults.passed++;
      } else {
        console.log('❌ No evolution content found');
        testResults.failed++;
        testResults.errors.push('No evolution content found');
      }
      
      // Look for tabs
      const tabs = await page.$$('[role="tab"], .tab, button');
      const tabNames = [];
      
      for (let tab of tabs) {
        const text = await tab.evaluate(el => el.textContent);
        if (text && text.length < 20) {
          tabNames.push(text);
        }
      }
      
      console.log(`Found potential tabs: ${tabNames.join(', ')}`);
      testResults.findings.push(`Potential tabs: ${tabNames.join(', ')}`);
      
    } catch (error) {
      console.error('❌ Evolution page content test failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Evolution content test error: ${error.message}`);
    }

    // Test 7: Test responsiveness
    console.log('\n🔍 Test 7: Testing responsiveness...');
    try {
      const viewports = [
        { width: 1920, height: 1080, name: 'Desktop' },
        { width: 768, height: 1024, name: 'Tablet' },
        { width: 375, height: 667, name: 'Mobile' }
      ];
      
      for (let viewport of viewports) {
        await page.setViewport(viewport);
        await page.waitForTimeout(1000);
        
        const responsiveScreenshot = path.join(screenshotsDir, `03-responsive-${viewport.name.toLowerCase()}.png`);
        await page.screenshot({ path: responsiveScreenshot, fullPage: true });
        testResults.screenshots.push(responsiveScreenshot);
        
        console.log(`  ✅ ${viewport.name} viewport test completed`);
        testResults.passed++;
      }
      
    } catch (error) {
      console.error('❌ Responsiveness test failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Responsiveness test error: ${error.message}`);
    }

    // Test 8: Performance check
    console.log('\n🔍 Test 8: Performance check...');
    try {
      const performanceMetrics = await page.evaluate(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        return {
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 0
        };
      });
      
      console.log(`Load time: ${performanceMetrics.loadTime}ms`);
      console.log(`DOM content loaded: ${performanceMetrics.domContentLoaded}ms`);
      console.log(`First paint: ${performanceMetrics.firstPaint}ms`);
      
      testResults.findings.push(`Load time: ${performanceMetrics.loadTime}ms`);
      testResults.findings.push(`DOM content loaded: ${performanceMetrics.domContentLoaded}ms`);
      testResults.findings.push(`First paint: ${performanceMetrics.firstPaint}ms`);
      
      if (performanceMetrics.loadTime < 3000) {
        console.log('✅ Page load time is acceptable');
        testResults.passed++;
      } else {
        console.log('⚠️  Page load time is slow');
        testResults.findings.push('Page load time is slow');
      }
      
    } catch (error) {
      console.error('❌ Performance check failed:', error.message);
      testResults.failed++;
      testResults.errors.push(`Performance check error: ${error.message}`);
    }

    // Final screenshot
    const finalScreenshot = path.join(screenshotsDir, '04-final-state.png');
    await page.screenshot({ path: finalScreenshot, fullPage: true });
    testResults.screenshots.push(finalScreenshot);

  } catch (error) {
    console.error('❌ Critical test failure:', error.message);
    testResults.failed++;
    testResults.errors.push(`Critical error: ${error.message}`);
  } finally {
    // Clean up
    if (page) {
      await page.close();
    }
    if (browser) {
      await browser.close();
    }
  }

  // Generate test report
  console.log('\n' + '='.repeat(80));
  console.log('📋 SETTINGS PAGES TEST REPORT');
  console.log('='.repeat(80));
  console.log(`✅ Tests Passed: ${testResults.passed}`);
  console.log(`❌ Tests Failed: ${testResults.failed}`);
  console.log(`⚠️  Findings: ${testResults.findings.length}`);
  console.log(`🚨 Errors: ${testResults.errors.length}`);
  console.log(`📸 Screenshots: ${testResults.screenshots.length}`);
  
  if (testResults.errors.length > 0) {
    console.log('\n🚨 ERRORS ENCOUNTERED:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }
  
  if (testResults.findings.length > 0) {
    console.log('\n⚠️  FINDINGS & OBSERVATIONS:');
    testResults.findings.forEach((finding, index) => {
      console.log(`${index + 1}. ${finding}`);
    });
  }
  
  if (testResults.screenshots.length > 0) {
    console.log('\n📸 SCREENSHOTS CAPTURED:');
    testResults.screenshots.forEach((screenshot, index) => {
      console.log(`${index + 1}. ${screenshot}`);
    });
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('Test completed!');
  console.log('='.repeat(80));

  return testResults;
}

// Run the test
if (require.main === module) {
  testSettingsPages().catch(console.error);
}

module.exports = { testSettingsPages };