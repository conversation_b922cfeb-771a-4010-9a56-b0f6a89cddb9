const fs = require('fs');
const path = require('path');

// Test UI Components by analyzing the code structure
async function testUIComponents() {
  const results = {
    timestamp: new Date().toISOString(),
    testResults: [],
    issues: [],
    recommendations: []
  };

  console.log('🔍 Testing UI Components via Code Analysis...\n');

  // Test 1: Navigation Component Structure
  console.log('1. Testing Navigation Component...');
  try {
    const navbarPath = path.join(__dirname, 'ui/components/Navbar.tsx');
    const navbarContent = fs.readFileSync(navbarPath, 'utf8');
    
    const hasNavigation = navbarContent.includes('Link href="/"') && 
                         navbarContent.includes('Link href="/memories"') &&
                         navbarContent.includes('Link href="/evolution"') &&
                         navbarContent.includes('Link href="/settings"');
    
    const hasActiveStates = navbarContent.includes('isActive') && 
                           navbarContent.includes('activeClass');
    
    const hasResponsiveDesign = navbarContent.includes('hidden sm:inline');
    
    results.testResults.push({
      test: 'Navigation Component Structure',
      passed: hasNavigation && hasActiveStates && hasResponsiveDesign,
      details: {
        navigation: hasNavigation,
        activeStates: hasActiveStates,
        responsive: hasResponsiveDesign
      }
    });
    
    console.log(`   ✓ Navigation links: ${hasNavigation ? 'PASS' : 'FAIL'}`);
    console.log(`   ✓ Active states: ${hasActiveStates ? 'PASS' : 'FAIL'}`);
    console.log(`   ✓ Responsive design: ${hasResponsiveDesign ? 'PASS' : 'FAIL'}`);
    
  } catch (error) {
    results.issues.push(`Navigation component test failed: ${error.message}`);
    console.log(`   ✗ Error: ${error.message}`);
  }

  // Test 2: Theme Toggle Component
  console.log('\n2. Testing Theme Toggle Component...');
  try {
    const themeTogglePath = path.join(__dirname, 'ui/components/theme-toggle.tsx');
    const themeToggleContent = fs.readFileSync(themeTogglePath, 'utf8');
    
    const hasThemeToggle = themeToggleContent.includes('useTheme');
    const hasAccessibility = themeToggleContent.includes('sr-only');
    const hasIcons = themeToggleContent.includes('Sun') && themeToggleContent.includes('Moon');
    
    results.testResults.push({
      test: 'Theme Toggle Component',
      passed: hasThemeToggle && hasAccessibility && hasIcons,
      details: {
        themeToggle: hasThemeToggle,
        accessibility: hasAccessibility,
        icons: hasIcons
      }
    });
    
    console.log(`   ✓ Theme toggle logic: ${hasThemeToggle ? 'PASS' : 'FAIL'}`);
    console.log(`   ✓ Accessibility: ${hasAccessibility ? 'PASS' : 'FAIL'}`);
    console.log(`   ✓ Icons: ${hasIcons ? 'PASS' : 'FAIL'}`);
    
  } catch (error) {
    results.issues.push(`Theme toggle test failed: ${error.message}`);
    console.log(`   ✗ Error: ${error.message}`);
  }

  // Test 3: 404 Page Component
  console.log('\n3. Testing 404 Page Component...');
  try {
    const notFoundPath = path.join(__dirname, 'ui/app/not-found.tsx');
    const notFoundContent = fs.readFileSync(notFoundPath, 'utf8');
    
    const has404Display = notFoundContent.includes('404');
    const hasHomeLink = notFoundContent.includes('Link href="/"');
    const hasCreativeDesign = notFoundContent.includes('bee-sketch');
    
    results.testResults.push({
      test: '404 Page Component',
      passed: has404Display && hasHomeLink,
      details: {
        display404: has404Display,
        homeLink: hasHomeLink,
        creativeDesign: hasCreativeDesign
      }
    });
    
    console.log(`   ✓ 404 display: ${has404Display ? 'PASS' : 'FAIL'}`);
    console.log(`   ✓ Home link: ${hasHomeLink ? 'PASS' : 'FAIL'}`);
    console.log(`   ✓ Creative design: ${hasCreativeDesign ? 'PASS' : 'FAIL'}`);
    
  } catch (error) {
    results.issues.push(`404 page test failed: ${error.message}`);
    console.log(`   ✗ Error: ${error.message}`);
  }

  // Test 4: Layout Component
  console.log('\n4. Testing Layout Component...');
  try {
    const layoutPath = path.join(__dirname, 'ui/app/layout.tsx');
    const layoutContent = fs.readFileSync(layoutPath, 'utf8');
    
    const hasThemeProvider = layoutContent.includes('ThemeProvider');
    const hasNavbar = layoutContent.includes('Navbar');
    const hasProviders = layoutContent.includes('Providers');
    
    results.testResults.push({
      test: 'Layout Component',
      passed: hasThemeProvider && hasNavbar && hasProviders,
      details: {
        themeProvider: hasThemeProvider,
        navbar: hasNavbar,
        providers: hasProviders
      }
    });
    
    console.log(`   ✓ Theme provider: ${hasThemeProvider ? 'PASS' : 'FAIL'}`);
    console.log(`   ✓ Navbar: ${hasNavbar ? 'PASS' : 'FAIL'}`);
    console.log(`   ✓ Providers: ${hasProviders ? 'PASS' : 'FAIL'}`);
    
  } catch (error) {
    results.issues.push(`Layout test failed: ${error.message}`);
    console.log(`   ✗ Error: ${error.message}`);
  }

  // Test 5: UI Components Library
  console.log('\n5. Testing UI Components Library...');
  try {
    const uiPath = path.join(__dirname, 'ui/components/ui');
    const uiComponents = fs.readdirSync(uiPath);
    
    const essentialComponents = [
      'button.tsx',
      'card.tsx',
      'dialog.tsx',
      'input.tsx',
      'table.tsx',
      'toast.tsx'
    ];
    
    const hasEssentialComponents = essentialComponents.every(comp => 
      uiComponents.includes(comp)
    );
    
    results.testResults.push({
      test: 'UI Components Library',
      passed: hasEssentialComponents,
      details: {
        totalComponents: uiComponents.length,
        essentialComponents: essentialComponents.length,
        hasEssential: hasEssentialComponents
      }
    });
    
    console.log(`   ✓ Total components: ${uiComponents.length}`);
    console.log(`   ✓ Essential components: ${hasEssentialComponents ? 'PASS' : 'FAIL'}`);
    
  } catch (error) {
    results.issues.push(`UI components test failed: ${error.message}`);
    console.log(`   ✗ Error: ${error.message}`);
  }

  // Test 6: Responsive Design Configuration
  console.log('\n6. Testing Responsive Design Configuration...');
  try {
    const tailwindPath = path.join(__dirname, 'ui/tailwind.config.ts');
    const tailwindContent = fs.readFileSync(tailwindPath, 'utf8');
    
    const hasResponsiveConfig = tailwindContent.includes('screens') || 
                               tailwindContent.includes('sm:') ||
                               tailwindContent.includes('md:') ||
                               tailwindContent.includes('lg:');
    
    results.testResults.push({
      test: 'Responsive Design Configuration',
      passed: hasResponsiveConfig,
      details: {
        tailwindConfig: hasResponsiveConfig
      }
    });
    
    console.log(`   ✓ Responsive configuration: ${hasResponsiveConfig ? 'PASS' : 'FAIL'}`);
    
  } catch (error) {
    results.issues.push(`Responsive design test failed: ${error.message}`);
    console.log(`   ✗ Error: ${error.message}`);
  }

  // Test 7: Accessibility Features
  console.log('\n7. Testing Accessibility Features...');
  try {
    const componentsPath = path.join(__dirname, 'ui/components');
    const files = fs.readdirSync(componentsPath, { recursive: true });
    
    let accessibilityFeatures = 0;
    let totalFiles = 0;
    
    files.forEach(file => {
      if (file.endsWith('.tsx') || file.endsWith('.ts')) {
        totalFiles++;
        const filePath = path.join(componentsPath, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        if (content.includes('aria-') || 
            content.includes('sr-only') || 
            content.includes('role=') ||
            content.includes('alt=')) {
          accessibilityFeatures++;
        }
      }
    });
    
    const accessibilityScore = accessibilityFeatures / totalFiles;
    
    results.testResults.push({
      test: 'Accessibility Features',
      passed: accessibilityScore > 0.5,
      details: {
        filesWithA11y: accessibilityFeatures,
        totalFiles: totalFiles,
        score: accessibilityScore
      }
    });
    
    console.log(`   ✓ Files with accessibility: ${accessibilityFeatures}/${totalFiles}`);
    console.log(`   ✓ Accessibility score: ${(accessibilityScore * 100).toFixed(1)}%`);
    
  } catch (error) {
    results.issues.push(`Accessibility test failed: ${error.message}`);
    console.log(`   ✗ Error: ${error.message}`);
  }

  // Test 8: Loading States
  console.log('\n8. Testing Loading States...');
  try {
    const skeletonPath = path.join(__dirname, 'ui/skeleton');
    const skeletonComponents = fs.readdirSync(skeletonPath);
    
    const loadingPath = path.join(__dirname, 'ui/app/loading.tsx');
    const hasLoadingPage = fs.existsSync(loadingPath);
    
    results.testResults.push({
      test: 'Loading States',
      passed: skeletonComponents.length > 0 && hasLoadingPage,
      details: {
        skeletonComponents: skeletonComponents.length,
        hasLoadingPage: hasLoadingPage
      }
    });
    
    console.log(`   ✓ Skeleton components: ${skeletonComponents.length}`);
    console.log(`   ✓ Loading page: ${hasLoadingPage ? 'PASS' : 'FAIL'}`);
    
  } catch (error) {
    results.issues.push(`Loading states test failed: ${error.message}`);
    console.log(`   ✗ Error: ${error.message}`);
  }

  // Generate Summary
  console.log('\n📊 TEST SUMMARY:');
  console.log('='.repeat(40));
  
  const passedTests = results.testResults.filter(t => t.passed).length;
  const totalTests = results.testResults.length;
  const successRate = ((passedTests / totalTests) * 100).toFixed(1);
  
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${totalTests - passedTests}`);
  console.log(`Success Rate: ${successRate}%`);
  
  console.log('\n📋 DETAILED RESULTS:');
  results.testResults.forEach((result, index) => {
    console.log(`${index + 1}. ${result.test}: ${result.passed ? '✅ PASS' : '❌ FAIL'}`);
  });
  
  if (results.issues.length > 0) {
    console.log('\n🚨 ISSUES FOUND:');
    results.issues.forEach(issue => console.log(`   - ${issue}`));
  }

  // Generate Recommendations
  console.log('\n🎯 RECOMMENDATIONS:');
  
  if (passedTests < totalTests) {
    console.log('\n❌ Failed Tests to Address:');
    results.testResults.filter(t => !t.passed).forEach(test => {
      console.log(`   - ${test.test}`);
    });
  }
  
  console.log('\n✅ General Recommendations:');
  console.log('   - Ensure all images have alt text');
  console.log('   - Add more ARIA labels for better accessibility');
  console.log('   - Test with real screen readers');
  console.log('   - Implement comprehensive keyboard navigation testing');
  console.log('   - Add more loading states for better UX');
  console.log('   - Consider implementing error boundaries');
  console.log('   - Add performance monitoring');
  console.log('   - Implement visual regression testing');
  
  // Save results
  fs.writeFileSync('ui-components-test-results.json', JSON.stringify(results, null, 2));
  console.log('\n📄 Results saved to: ui-components-test-results.json');
  
  return results;
}

// Run the test
testUIComponents().catch(console.error);