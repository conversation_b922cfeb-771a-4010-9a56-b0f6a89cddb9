#!/usr/bin/env python3
"""
Quick test script to verify the simplified configuration endpoints work correctly.
"""
import requests
import json
import sys
import os

# Add the api directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'api'))

# Test the simplified configuration endpoints
API_BASE = "http://localhost:8765"

def test_simple_config_endpoints():
    """Test the simplified configuration endpoints"""
    print("Testing Simplified Configuration Endpoints...")
    
    # Test 1: GET /api/v1/config/simple
    print("\n1. Testing GET /api/v1/config/simple")
    try:
        response = requests.get(f"{API_BASE}/api/v1/config/simple")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {json.dumps(data, indent=2)}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 2: POST /api/v1/config/simple
    print("\n2. Testing POST /api/v1/config/simple")
    test_config = {
        "openaiApiKey": "test-key",
        "userId": "test-user",
        "customPrompt": "Test prompt for memory processing",
        "systemEnabled": True
    }
    try:
        response = requests.post(
            f"{API_BASE}/api/v1/config/simple",
            json=test_config,
            headers={"Content-Type": "application/json"}
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {json.dumps(data, indent=2)}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    # Test 3: POST /api/v1/config/reset
    print("\n3. Testing POST /api/v1/config/reset")
    try:
        response = requests.post(f"{API_BASE}/api/v1/config/reset")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {json.dumps(data, indent=2)}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Connection error: {e}")
    
    print("\nTest completed!")

if __name__ == "__main__":
    test_simple_config_endpoints()