{"timestamp": "2025-07-04T13:33:35.736Z", "testResults": [{"test": "Navigation Component Structure", "passed": true, "details": {"navigation": true, "activeStates": true, "responsive": true}}, {"test": "Theme Toggle Component", "passed": true, "details": {"themeToggle": true, "accessibility": true, "icons": true}}, {"test": "404 Page Component", "passed": true, "details": {"display404": true, "homeLink": true, "creativeDesign": true}}, {"test": "Layout Component", "passed": true, "details": {"themeProvider": true, "navbar": true, "providers": true}}, {"test": "UI Components Library", "passed": true, "details": {"totalComponents": 50, "essentialComponents": 6, "hasEssential": true}}, {"test": "Responsive Design Configuration", "passed": true, "details": {"tailwindConfig": true}}, {"test": "Accessibility Features", "passed": false, "details": {"filesWithA11y": 17, "totalFiles": 61, "score": 0.2786885245901639}}, {"test": "Loading States", "passed": true, "details": {"skeletonComponents": 3, "hasLoadingPage": true}}], "issues": [], "recommendations": []}