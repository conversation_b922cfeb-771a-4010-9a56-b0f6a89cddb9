# syntax=docker.io/docker/dockerfile:1

# Base stage for common setup
FROM node:18-alpine AS base

# Install dependencies for pnpm
RUN apk add --no-cache libc6-compat curl && \
    corepack enable && \
    corepack prepare pnpm@latest --activate

WORKDIR /app

FROM base AS deps

COPY package.json pnpm-lock.yaml ./

# Use --no-frozen-lockfile to avoid lockfile issues
RUN pnpm install --no-frozen-lockfile

FROM base AS builder
WORKDIR /app

# Accept build arguments with default values for portability
ARG NEXT_PUBLIC_API_URL=http://localhost:8765
ARG NEXT_PUBLIC_AUTH_ENABLED=false

# Set environment variables for Next.js build
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_AUTH_ENABLED=$NEXT_PUBLIC_AUTH_ENABLED
ENV NEXT_TELEMETRY_DISABLED=1

COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Copy next config for development
RUN cp next.config.dev.mjs next.config.mjs

# Build the application
RUN pnpm build

FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Create user and group
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Create the entrypoint script directly in the Dockerfile
RUN echo '#!/bin/sh' > /app/entrypoint.sh && \
    echo 'set -e' >> /app/entrypoint.sh && \
    echo 'cd /app' >> /app/entrypoint.sh && \
    echo 'echo "Starting TradeLink Memory UI on port 3000..."' >> /app/entrypoint.sh && \
    echo 'echo "API URL: $NEXT_PUBLIC_API_URL"' >> /app/entrypoint.sh && \
    echo 'printenv | grep NEXT_PUBLIC_ | while read -r line; do' >> /app/entrypoint.sh && \
    echo '  key=$(echo $line | cut -d "=" -f1)' >> /app/entrypoint.sh && \
    echo '  value=$(echo $line | cut -d "=" -f2)' >> /app/entrypoint.sh && \
    echo '  find .next/ -type f -exec sed -i "s#$key#$value#g" {} \;' >> /app/entrypoint.sh && \
    echo 'done' >> /app/entrypoint.sh && \
    echo 'echo "Done replacing env variables NEXT_PUBLIC_ with real values"' >> /app/entrypoint.sh && \
    echo 'exec "$@"' >> /app/entrypoint.sh && \
    chmod +x /app/entrypoint.sh && \
    chown nextjs:nodejs /app/entrypoint.sh

USER nextjs

EXPOSE 3000
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["node", "server.js"]
