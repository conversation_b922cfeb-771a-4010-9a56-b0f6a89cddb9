'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Activity, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { useEvolutionStatus } from '@/hooks/useEvolutionStatus';

export default function EvolutionPage() {
  const { status, isLoading, error } = useEvolutionStatus();

  if (isLoading) {
    return <div className="flex items-center justify-center h-64">Loading...</div>;
  }

  if (error || !status) {
    return <div className="flex items-center justify-center h-64">Error loading status: {error}</div>;
  }

  const getStatusIcon = () => {
    switch (status.status) {
      case 'healthy': return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'degraded': return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'error': return <AlertCircle className="w-5 h-5 text-red-500" />;
    }
  };

  const getStatusColor = () => {
    switch (status.status) {
      case 'healthy': return 'default';
      case 'degraded': return 'secondary';
      case 'error': return 'destructive';
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Evolution Intelligence</h1>
        <p className="text-muted-foreground">
          Monitor how memories are being processed and evolved
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* System Status */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            {getStatusIcon()}
          </CardHeader>
          <CardContent>
            <Badge variant={getStatusColor()}>
              {status.status.charAt(0).toUpperCase() + status.status.slice(1)}
            </Badge>
          </CardContent>
        </Card>

        {/* Today's Operations */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Operations</CardTitle>
            <Activity className="w-4 h-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{status.todayOperations}</div>
          </CardContent>
        </Card>

        {/* Total Operations */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Operations</CardTitle>
            <Clock className="w-4 h-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{status.totalOperations}</div>
          </CardContent>
        </Card>

        {/* Last Operation */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Operation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-muted-foreground">
              {status.lastOperation ? new Date(status.lastOperation).toLocaleString() : 'None'}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Operation Breakdown */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Operation Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{status.operationCounts.add}</div>
              <div className="text-sm text-muted-foreground">Added</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{status.operationCounts.update}</div>
              <div className="text-sm text-muted-foreground">Updated</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{status.operationCounts.delete}</div>
              <div className="text-sm text-muted-foreground">Deleted</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">{status.operationCounts.noop}</div>
              <div className="text-sm text-muted-foreground">No Change</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}