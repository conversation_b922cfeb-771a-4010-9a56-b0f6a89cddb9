"use client";

import { Install } from "@/components/dashboard/Install";
import Stats from "@/components/dashboard/Stats";
import { UserSelector } from "@/components/dashboard/UserSelector";
import { MemoryFilters } from "@/app/memories/components/MemoryFilters";
import { MemoriesSection } from "@/app/memories/components/MemoriesSection";
import "@/styles/animation.css";

export default function DashboardPage() {
  return (
    <div className="text-white py-6">
      <div className="container">
        <div className="w-full mx-auto space-y-6">
          {/* Main page title */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">MemoryMaster Dashboard</h1>
            <p className="text-zinc-400 animate-fade-slide-down">Manage your memories and MCP integrations</p>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* User Selection */}
            <div className="col-span-1 animate-fade-slide-down">
              <UserSelector />
            </div>

            {/* Memory Category Breakdown */}
            <div className="col-span-1 lg:col-span-2 animate-fade-slide-down delay-1">
              <Install />
            </div>

            {/* Memories Stats */}
            <div className="col-span-1 animate-fade-slide-down delay-2">
              <Stats />
            </div>
          </div>

          <div>
            <div className="animate-fade-slide-down delay-3">
              <MemoryFilters />
            </div>
            <div className="animate-fade-slide-down delay-4">
              <MemoriesSection />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
