"use client";

import { Provider } from "react-redux";
import { store } from "../store/store";
import { useEffect } from "react";
import { useConfig } from "@/hooks/useConfig";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store/store";
import { loadSelectedUserFromStorage } from "@/store/userSlice";

function ConfigInitializer({ children }: { children: React.ReactNode }) {
  const { fetchConfig } = useConfig();
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    // Initialize config on app startup
    fetchConfig();
    // Load selected user from localStorage
    dispatch(loadSelectedUserFromStorage());
  }, [fetchConfig, dispatch]);

  return <>{children}</>;
}

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <Provider store={store}>
      <ConfigInitializer>
        {children}
      </ConfigInitializer>
    </Provider>
  );
}
