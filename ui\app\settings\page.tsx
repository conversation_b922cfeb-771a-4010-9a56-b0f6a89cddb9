'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Save, RotateCcw } from 'lucide-react';
import { useConfig } from '@/hooks/useConfig';
import { SimplifiedSettings } from '@/store/configSlice';
import { useToast } from '@/components/ui/use-toast';

export default function SettingsPage() {
  const { toast } = useToast();
  const { settings, status, error, fetchConfig, saveConfig, resetConfig, isLoading } = useConfig();
  const [localSettings, setLocalSettings] = useState<SimplifiedSettings>(settings);
  const [systemStatus, setSystemStatus] = useState<'healthy' | 'error'>('healthy');
  
  const isSaving = isLoading;

  useEffect(() => {
    fetchSettings();
    checkSystemStatus();
  }, []);

  // Update local settings when Redux state changes
  useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  const fetchSettings = async () => {
    try {
      await fetchConfig();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load settings',
        variant: 'destructive',
      });
    }
  };

  const checkSystemStatus = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/health`);
      setSystemStatus(response.ok ? 'healthy' : 'error');
    } catch (error) {
      setSystemStatus('error');
    }
  };

  const saveSettings = async () => {
    try {
      await saveConfig(localSettings);
      toast({
        title: 'Settings saved',
        description: 'Your settings have been saved successfully.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save settings. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const resetToDefaults = async () => {
    if (confirm('Reset all settings to defaults? This cannot be undone.')) {
      try {
        await resetConfig();
        toast({
          title: 'Settings reset',
          description: 'Settings have been reset to defaults.',
        });
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to reset settings.',
          variant: 'destructive',
        });
      }
    }
  };

  if (isLoading) {
    return <div className="flex items-center justify-center h-64">Loading settings...</div>;
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-2xl">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Settings</h1>
        <p className="text-muted-foreground">
          Basic configuration for your memory system
        </p>
      </div>

      <div className="space-y-6">
        {/* System Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              System Status
              <Badge variant={systemStatus === 'healthy' ? 'default' : 'destructive'}>
                {systemStatus === 'healthy' ? 'Healthy' : 'Error'}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              {systemStatus === 'healthy' 
                ? 'All systems are operating normally.' 
                : 'System issues detected. Check logs for details.'}
            </p>
          </CardContent>
        </Card>

        {/* Basic Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="userId">User ID</Label>
              <Input
                id="userId"
                value={localSettings.userId}
                onChange={(e) => setLocalSettings({ ...localSettings, userId: e.target.value })}
                placeholder="Your user identifier"
              />
              <p className="text-xs text-muted-foreground mt-1">
                This identifies your memories in the system
              </p>
            </div>

            <div>
              <Label htmlFor="openaiApiKey">OpenAI API Key</Label>
              <Input
                id="openaiApiKey"
                type="password"
                value={localSettings.openaiApiKey}
                onChange={(e) => setLocalSettings({ ...localSettings, openaiApiKey: e.target.value })}
                placeholder="sk-..."
              />
              <p className="text-xs text-muted-foreground mt-1">
                Required for memory processing and evolution
              </p>
            </div>

            <div>
              <Label htmlFor="customPrompt">Custom Memory Processing Prompt (Optional)</Label>
              <Textarea
                id="customPrompt"
                value={localSettings.customPrompt}
                onChange={(e) => setLocalSettings({ ...localSettings, customPrompt: e.target.value })}
                placeholder="Custom instructions for memory processing..."
                className="min-h-24"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Leave empty to use default prompt optimized for dropshipping business
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex gap-2">
          <Button onClick={saveSettings} disabled={isSaving}>
            <Save className="w-4 h-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Settings'}
          </Button>
          <Button variant="outline" onClick={resetToDefaults}>
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset to Defaults
          </Button>
        </div>

        {/* Quick Info */}
        <Card>
          <CardHeader>
            <CardTitle>System Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">Environment</p>
                <p className="font-medium">Production</p>
              </div>
              <div>
                <p className="text-muted-foreground">User</p>
                <p className="font-medium">{localSettings.userId}</p>
              </div>
              <div>
                <p className="text-muted-foreground">API Status</p>
                <p className="font-medium">
                  {localSettings.openaiApiKey ? 'Configured' : 'Not Configured'}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">System</p>
                <p className="font-medium">
                  {localSettings.systemEnabled ? 'Enabled' : 'Disabled'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
