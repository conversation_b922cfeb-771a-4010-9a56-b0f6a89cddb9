"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { HiH<PERSON>, HiMiniRectangleStack } from "react-icons/hi2";
import { FiRefreshCcw } from "react-icons/fi";
import { TrendingUp, Settings } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { CreateMemoryDialog } from "@/app/memories/components/CreateMemoryDialog";
import { useMemoriesApi } from "@/hooks/useMemoriesApi";
import Image from "next/image";
import { useStats } from "@/hooks/useStats";
import { ThemeToggle } from "@/components/theme-toggle";
import { UserSelector } from "@/components/UserSelector";
import { UserIndicator } from "@/components/UserIndicator";

export function Navbar() {
  const pathname = usePathname();

  const memoriesApi = useMemoriesApi();
  const statsApi = useStats();

  // Define route matchers with typed parameter extraction
  const routeBasedFetchMapping: {
    match: RegExp;
    getFetchers: (params: Record<string, string>) => (() => Promise<any>)[];
  }[] = [
    {
      match: /^\/memory\/([^/]+)$/,
      getFetchers: ({ memory_id }) => [
        () => memoriesApi.fetchMemoryById(memory_id),
        () => memoriesApi.fetchAccessLogs(memory_id),
        () => memoriesApi.fetchRelatedMemories(memory_id),
      ],
    },
    {
      match: /^\/memories$/,
      getFetchers: () => [memoriesApi.fetchMemories],
    },
    {
      match: /^\/$/,
      getFetchers: () => [statsApi.fetchStats, memoriesApi.fetchMemories],
    },
    {
      match: /^\/settings$/,
      getFetchers: () => [],
    },
    {
      match: /^\/evolution$/,
      getFetchers: () => [statsApi.fetchStats],
    },
  ];

  const getFetchersForPath = (path: string) => {
    for (const route of routeBasedFetchMapping) {
      const match = path.match(route.match);
      if (match) {
        if (route.match.source.includes("memory")) {
          return route.getFetchers({ memory_id: match[1] });
        }
        return route.getFetchers({});
      }
    }
    return [];
  };

  const handleRefresh = async () => {
    const fetchers = getFetchersForPath(pathname);
    await Promise.allSettled(fetchers.map((fn) => fn()));
  };


  const isActive = (href: string) => {
    if (href === "/") return pathname === href;
    return pathname.startsWith(href.substring(0, 5));
  };

  const activeClass = "bg-zinc-800 text-white border-zinc-600";
  const inactiveClass = "text-zinc-300";

  return (
    <header className="sticky top-0 z-50 w-full border-b border-zinc-800 bg-zinc-950/95 backdrop-blur supports-[backdrop-filter]:bg-zinc-950/60 overflow-x-hidden">
      <div className="container flex h-14 items-center justify-between max-w-full px-2 sm:px-4">
        <Link href="/" className="flex items-center gap-2 min-w-0 flex-shrink-0">
          <Image src="/logo.svg" alt="MemoryMaster" width={26} height={26} />
          <span className="text-lg sm:text-xl font-medium truncate">MemoryMaster</span>
        </Link>
        <div className="flex items-center gap-1 sm:gap-2 overflow-x-auto flex-shrink min-w-0">
          <Link href="/">
            <Button
              variant="outline"
              size="sm"
              className={`flex items-center gap-1 sm:gap-2 border-none text-xs sm:text-sm px-2 sm:px-3 ${
                isActive("/") ? activeClass : inactiveClass
              }`}
            >
              <HiHome className="h-4 w-4" />
              <span className="hidden sm:inline">Dashboard</span>
            </Button>
          </Link>
          <Link href="/memories">
            <Button
              variant="outline"
              size="sm"
              className={`flex items-center gap-1 sm:gap-2 border-none text-xs sm:text-sm px-2 sm:px-3 ${
                isActive("/memories") ? activeClass : inactiveClass
              }`}
            >
              <HiMiniRectangleStack className="h-4 w-4" />
              <span className="hidden sm:inline">Memories</span>
            </Button>
          </Link>
          <Link href="/evolution">
            <Button
              variant="outline"
              size="sm"
              className={`flex items-center gap-1 sm:gap-2 border-none text-xs sm:text-sm px-2 sm:px-3 ${
                isActive("/evolution") ? activeClass : inactiveClass
              }`}
            >
              <TrendingUp className="h-4 w-4" />
              <span className="hidden sm:inline">Evolution</span>
            </Button>
          </Link>
          <Link href="/settings">
            <Button
              variant="outline"
              size="sm"
              className={`flex items-center gap-1 sm:gap-2 border-none text-xs sm:text-sm px-2 sm:px-3 ${
                isActive("/settings") ? activeClass : inactiveClass
              }`}
            >
              <Settings className="h-4 w-4" />
              <span className="hidden sm:inline">Settings</span>
            </Button>
          </Link>
        </div>
        <div className="flex items-center gap-2 sm:gap-4 flex-shrink-0">
          <UserSelector />
          <UserIndicator />
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="sm"
            className="border-zinc-700/50 bg-zinc-900 hover:bg-zinc-800 px-2 sm:px-3"
          >
            <FiRefreshCcw className="h-4 w-4 transition-transform duration-300 group-hover:rotate-180" />
            <span className="hidden sm:inline ml-2">Refresh</span>
          </Button>
          <CreateMemoryDialog />
          <ThemeToggle />
        </div>
      </div>
    </header>
  );
}
