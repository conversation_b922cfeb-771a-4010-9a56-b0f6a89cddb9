'use client';

import React from 'react';
import { useSelectedUser } from '@/hooks/useSelectedUser';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { User as UserIcon, Crown } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

export function UserIndicator() {
  const { selectedUser, isLoading, error } = useSelectedUser();

  if (isLoading) {
    return (
      <div className="flex items-center gap-2">
        <Skeleton className="h-6 w-6 rounded-full bg-zinc-800" />
        <Skeleton className="h-4 w-16 bg-zinc-800" />
      </div>
    );
  }

  if (error || !selectedUser) {
    return (
      <div className="flex items-center gap-2 text-zinc-500">
        <UserIcon className="h-4 w-4" />
        <span className="text-xs hidden sm:inline">No user</span>
      </div>
    );
  }

  const getInitials = (user: typeof selectedUser) => {
    if (user.name) {
      return user.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    return user.user_id.slice(0, 2).toUpperCase();
  };

  const getDisplayName = (user: typeof selectedUser) => {
    return user.name || user.user_id;
  };

  return (
    <div className="flex items-center gap-2">
      <Avatar className="h-6 w-6">
        <AvatarFallback className="bg-zinc-700 text-white text-xs">
          {getInitials(selectedUser)}
        </AvatarFallback>
      </Avatar>
      <div className="flex items-center gap-1">
        <span className="text-xs text-zinc-300 hidden sm:inline truncate max-w-20">
          {getDisplayName(selectedUser)}
        </span>
        {selectedUser.email_verified && (
          <Crown className="h-3 w-3 text-yellow-400" />
        )}
      </div>
    </div>
  );
}