"use client";

import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store/store';
import { fetchUsers, setSelectedUser, loadSelectedUserFromStorage } from '@/store/userSlice';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { User } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

export function UserSelector() {
  const dispatch = useDispatch();
  const { availableUsers, selectedUser, isLoading, error } = useSelector(
    (state: RootState) => state.user
  );

  useEffect(() => {
    // Load selected user from storage on mount
    dispatch(loadSelectedUserFromStorage());
    
    // Fetch available users
    dispatch(fetchUsers());
  }, [dispatch]);

  const handleUserChange = (userId: string) => {
    const user = availableUsers.find(u => u.user_id === userId);
    if (user) {
      dispatch(setSelectedUser(user));
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center gap-2">
        <User className="h-4 w-4 text-zinc-400" />
        <span className="text-sm text-zinc-400">Loading users...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center gap-2">
        <User className="h-4 w-4 text-red-400" />
        <Badge variant="destructive" className="text-xs">
          Error loading users
        </Badge>
      </div>
    );
  }

  if (availableUsers.length === 0) {
    return (
      <div className="flex items-center gap-2">
        <User className="h-4 w-4 text-zinc-400" />
        <span className="text-sm text-zinc-400">No users found</span>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <User className="h-4 w-4 text-zinc-400" />
      <Select
        value={selectedUser?.user_id || ''}
        onValueChange={handleUserChange}
      >
        <SelectTrigger className="w-[180px] bg-zinc-900 border-zinc-700 text-white">
          <SelectValue placeholder="Select user">
            {selectedUser ? (
              <div className="flex items-center gap-2">
                <span className="truncate">{selectedUser.name || selectedUser.user_id}</span>
                {selectedUser.email_verified && (
                  <Badge variant="outline" className="text-xs bg-green-900 text-green-300 border-green-700">
                    Verified
                  </Badge>
                )}
              </div>
            ) : (
              'Select user'
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="bg-zinc-900 border-zinc-700">
          {availableUsers.map((user) => (
            <SelectItem 
              key={user.user_id} 
              value={user.user_id}
              className="text-white hover:bg-zinc-800 focus:bg-zinc-800"
            >
              <div className="flex items-center justify-between w-full">
                <div className="flex flex-col items-start">
                  <span className="font-medium">
                    {user.name || user.user_id}
                  </span>
                  <span className="text-xs text-zinc-400">
                    {user.email}
                  </span>
                </div>
                {user.email_verified && (
                  <Badge variant="outline" className="text-xs bg-green-900 text-green-300 border-green-700 ml-2">
                    ✓
                  </Badge>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}

export default UserSelector;