'use client';

import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store/store';
import { fetchUsers, setSelectedUser, loadSelectedUserFromStorage, User } from '@/store/userSlice';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { User as UserIcon, Crown, Clock } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

export function UserSelector() {
  const dispatch = useDispatch<AppDispatch>();
  const { availableUsers, selectedUser, isLoading, error } = useSelector((state: RootState) => state.user);

  useEffect(() => {
    // Load selected user from storage first
    dispatch(loadSelectedUserFromStorage());
    // Then fetch available users
    dispatch(fetchUsers());
  }, [dispatch]);

  const handleUserChange = (userId: string) => {
    const user = availableUsers.find(u => u.user_id === userId);
    if (user) {
      dispatch(setSelectedUser(user));
    }
  };

  const getInitials = (user: User) => {
    if (user.name) {
      return user.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    return user.user_id.slice(0, 2).toUpperCase();
  };

  const getDisplayName = (user: User) => {
    return user.name || user.user_id;
  };

  const formatLastSignIn = (lastSignIn?: string) => {
    if (!lastSignIn) return 'Never';
    const date = new Date(lastSignIn);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return date.toLocaleDateString();
  };

  if (isLoading) {
    return (
      <Card className="bg-zinc-900 border-zinc-800">
        <CardHeader className="pb-3">
          <CardTitle className="text-white flex items-center gap-2">
            <UserIcon className="h-5 w-5" />
            Active User
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <Skeleton className="h-10 w-full bg-zinc-800" />
          <div className="flex items-center gap-3">
            <Skeleton className="h-10 w-10 rounded-full bg-zinc-800" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-24 bg-zinc-800" />
              <Skeleton className="h-3 w-16 bg-zinc-800" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="bg-zinc-900 border-zinc-800">
        <CardHeader className="pb-3">
          <CardTitle className="text-white flex items-center gap-2">
            <UserIcon className="h-5 w-5" />
            Active User
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-400 text-sm">Error loading users: {error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-zinc-900 border-zinc-800">
      <CardHeader className="pb-3">
        <CardTitle className="text-white flex items-center gap-2">
          <UserIcon className="h-5 w-5" />
          Active User
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Select
          value={selectedUser?.user_id || ''}
          onValueChange={handleUserChange}
        >
          <SelectTrigger className="bg-zinc-800 border-zinc-700 text-white">
            <SelectValue placeholder="Select a user" />
          </SelectTrigger>
          <SelectContent className="bg-zinc-800 border-zinc-700">
            {availableUsers.map((user) => (
              <SelectItem 
                key={user.user_id} 
                value={user.user_id}
                className="text-white hover:bg-zinc-700 focus:bg-zinc-700"
              >
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarFallback className="bg-zinc-700 text-white text-xs">
                      {getInitials(user)}
                    </AvatarFallback>
                  </Avatar>
                  <span>{getDisplayName(user)}</span>
                  {user.email_verified && (
                    <Crown className="h-3 w-3 text-yellow-400" />
                  )}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {selectedUser && (
          <div className="flex items-center gap-3 p-3 bg-zinc-800 rounded-lg">
            <Avatar className="h-10 w-10">
              <AvatarFallback className="bg-zinc-700 text-white">
                {getInitials(selectedUser)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <p className="text-white font-medium truncate">
                  {getDisplayName(selectedUser)}
                </p>
                {selectedUser.email_verified && (
                  <Badge variant="secondary" className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">
                    <Crown className="h-3 w-3 mr-1" />
                    Verified
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-1 text-xs text-zinc-400">
                <Clock className="h-3 w-3" />
                <span>Last active: {formatLastSignIn(selectedUser.last_sign_in_at)}</span>
              </div>
              {selectedUser.email && (
                <p className="text-xs text-zinc-500 truncate mt-1">
                  {selectedUser.email}
                </p>
              )}
            </div>
          </div>
        )}

        <div className="text-xs text-zinc-500">
          {availableUsers.length} user{availableUsers.length !== 1 ? 's' : ''} available
        </div>
      </CardContent>
    </Card>
  );
}