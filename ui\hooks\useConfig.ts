import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store/store';
import { 
  updateSettings, 
  setConfigLoading, 
  setConfigSuccess, 
  setConfigError,
  SimplifiedSettings 
} from '@/store/configSlice';

export const useConfig = () => {
  const dispatch = useDispatch();
  const { settings, status, error } = useSelector((state: RootState) => state.config);

  const fetchConfig = async () => {
    dispatch(setConfigLoading());
    try {
      // Load from localStorage
      const savedSettings = localStorage.getItem('memoryMasterSettings');
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        dispatch(setConfigSuccess(parsedSettings));
      } else {
        // Use default settings - user will need to configure user ID
        const defaultSettings: SimplifiedSettings = {
          openaiApiKey: '',
          userId: '',
          customPrompt: '',
          systemEnabled: true,
        };
        dispatch(setConfigSuccess(defaultSettings));
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error);
      dispatch(setConfigError('Failed to load settings'));
    }
  };

  const saveConfig = async (newSettings: SimplifiedSettings) => {
    dispatch(setConfigLoading());
    try {
      // Save to localStorage
      localStorage.setItem('memoryMasterSettings', JSON.stringify(newSettings));
      
      // Update Redux store
      dispatch(updateSettings(newSettings));
      dispatch(setConfigSuccess(newSettings));
    } catch (error) {
      console.error('Save error:', error);
      dispatch(setConfigError('Failed to save settings'));
      throw error;
    }
  };

  const resetConfig = async () => {
    dispatch(setConfigLoading());
    try {
      const defaultSettings: SimplifiedSettings = {
        openaiApiKey: '',
        userId: '',
        customPrompt: '',
        systemEnabled: true,
      };
      
      localStorage.removeItem('memoryMasterSettings');
      dispatch(setConfigSuccess(defaultSettings));
    } catch (error) {
      console.error('Reset error:', error);
      dispatch(setConfigError('Failed to reset settings'));
      throw error;
    }
  };

  return {
    settings,
    status,
    error,
    fetchConfig,
    saveConfig,
    resetConfig,
    isLoading: status === 'loading',
  };
};