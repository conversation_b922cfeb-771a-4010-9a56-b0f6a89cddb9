'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/store';
import { useSelectedUser } from '@/hooks/useSelectedUser';

/**
 * Interface for evolution status data
 */
interface EvolutionStatus {
  status: 'healthy' | 'degraded' | 'error';
  totalOperations: number;
  todayOperations: number;
  lastOperation: string;
  operationCounts: {
    add: number;
    update: number;
    delete: number;
    noop: number;
  };
}

/**
 * Hook return type
 */
interface UseEvolutionStatusReturn {
  status: EvolutionStatus | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Simplified evolution status hook for monitoring system health and operation counts
 * 
 * Features:
 * - Fetches evolution status from API
 * - Handles loading and error states
 * - Automatic refresh every 30 seconds
 * - Returns system health and operation statistics
 * 
 * @returns Object containing status data, loading state, error, and refetch function
 */
export function useEvolutionStatus(): UseEvolutionStatusReturn {
  const [status, setStatus] = useState<EvolutionStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const { selectedUser } = useSelectedUser();
  const fallbackUserId = useSelector((state: RootState) => state.config.settings.userId) || 'default-user';
  const user_id = selectedUser?.user_id || fallbackUserId;

  const URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8765";

  /**
   * Fetch evolution status from the API
   */
  const fetchStatus = useCallback(async (): Promise<void> => {
    try {
      setError(null);
      const response = await fetch(`${URL}/api/v1/evolution/status?user_id=${encodeURIComponent(user_id)}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch evolution status: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Validate response structure
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid response format');
      }
      
      // Set status with validation
      setStatus({
        status: data.status || 'error',
        totalOperations: data.totalOperations || 0,
        todayOperations: data.todayOperations || 0,
        lastOperation: data.lastOperation || '',
        operationCounts: {
          add: data.operationCounts?.add || 0,
          update: data.operationCounts?.update || 0,
          delete: data.operationCounts?.delete || 0,
          noop: data.operationCounts?.noop || 0,
        }
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Evolution status fetch error:', errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [URL, user_id]);

  /**
   * Set up automatic refresh and initial fetch
   */
  useEffect(() => {
    // Initial fetch
    fetchStatus();
    
    // Set up automatic refresh every 30 seconds
    const interval = setInterval(fetchStatus, 30000);
    
    // Cleanup interval on unmount
    return () => clearInterval(interval);
  }, [fetchStatus]);

  return {
    status,
    isLoading,
    error,
    refetch: fetchStatus,
  };
}