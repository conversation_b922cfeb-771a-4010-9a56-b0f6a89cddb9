import { useSelector } from 'react-redux';
import { RootState } from '@/store/store';
import { User } from '@/store/userSlice';

export function useSelectedUser(): {
  selectedUser: User | null;
  isLoading: boolean;
  error: string | null;
} {
  const { selectedUser, isLoading, error } = useSelector((state: RootState) => state.user);
  
  return {
    selectedUser,
    isLoading,
    error,
  };
}

export default useSelectedUser;