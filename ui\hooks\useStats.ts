import { useState, useCallback } from 'react';
import axios from 'axios';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store/store';
import { setStats } from '@/store/memoriesSlice';
import { useSelectedUser } from '@/hooks/useSelectedUser';

// Define the new simplified memory type
export interface SimpleMemory {
  id: string;
  text: string;
  created_at: string;
  state: string;
  categories: string[];
  app_name: string;
}

// Define the shape of the API response item
interface APIStatsResponse {
  total_memories: number;
  total_apps: number;
  apps: any[];
}


interface UseStatsApiReturn {
  fetchStats: () => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

export const useStats = (): UseStatsApiReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const dispatch = useDispatch<AppDispatch>();
  const { selectedUser } = useSelectedUser();
  const fallbackUserId = useSelector((state: RootState) => state.config.settings.userId) || 'default-user';
  const user_id = selectedUser?.user_id || fallbackUserId;

  const URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8765";

  const fetchStats = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await axios.get<APIStatsResponse>(
        `${URL}/api/v1/stats?user_id=${encodeURIComponent(user_id)}`
      );
      
      // Dispatch stats to the store
      dispatch(setStats({
        totalMemories: response.data.total_memories,
        totalApps: response.data.total_apps,
        apps: response.data.apps,
      }));
      
      console.log('Stats fetched:', response.data);
      setIsLoading(false);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch stats';
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  }, [dispatch, URL, user_id]);

  return { fetchStats, isLoading, error };
};