import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface SimplifiedSettings {
  openaiApiKey: string;
  userId: string;
  customPrompt: string;
  systemEnabled: boolean;
}

export interface ConfigState {
  settings: SimplifiedSettings;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
}

const initialState: ConfigState = {
  settings: {
    openaiApiKey: '',
    userId: '',
    customPrompt: '',
    systemEnabled: true,
  },
  status: 'idle',
  error: null,
};

const configSlice = createSlice({
  name: 'config',
  initialState,
  reducers: {
    setConfigLoading: (state) => {
      state.status = 'loading';
      state.error = null;
    },
    setConfigSuccess: (state, action: PayloadAction<SimplifiedSettings>) => {
      state.settings = action.payload;
      state.status = 'succeeded';
      state.error = null;
    },
    setConfigError: (state, action: PayloadAction<string>) => {
      state.status = 'failed';
      state.error = action.payload;
    },
    updateSettings: (state, action: PayloadAction<Partial<SimplifiedSettings>>) => {
      state.settings = { ...state.settings, ...action.payload };
    },
    updateOpenaiApiKey: (state, action: PayloadAction<string>) => {
      state.settings.openaiApiKey = action.payload;
    },
    updateUserId: (state, action: PayloadAction<string>) => {
      state.settings.userId = action.payload;
    },
    updateCustomPrompt: (state, action: PayloadAction<string>) => {
      state.settings.customPrompt = action.payload;
    },
    updateSystemEnabled: (state, action: PayloadAction<boolean>) => {
      state.settings.systemEnabled = action.payload;
    },
  },
});

export const {
  setConfigLoading,
  setConfigSuccess,
  setConfigError,
  updateSettings,
  updateOpenaiApiKey,
  updateUserId,
  updateCustomPrompt,
  updateSystemEnabled,
} = configSlice.actions;

export default configSlice.reducer; 