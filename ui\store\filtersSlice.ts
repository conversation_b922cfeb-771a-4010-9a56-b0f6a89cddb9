import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Category {
  id: string;
  name: string;
  description: string;
  updated_at: string;
  created_at: string;
}

export interface MemoryFiltersState {
  selectedCategories: string[];
  sortColumn: string;
  sortDirection: 'asc' | 'desc';
  showArchived: boolean;
  categories: {
    items: Category[];
    total: number;
    isLoading: boolean;
    error: string | null;
  };
}

const initialState: MemoryFiltersState = {
  selectedCategories: [],
  sortColumn: 'created_at',
  sortDirection: 'desc',
  showArchived: false,
  categories: {
    items: [],
    total: 0,
    isLoading: false,
    error: null
  }
};

const filtersSlice = createSlice({
  name: 'filters',
  initialState,
  reducers: {
    setCategoriesLoading: (state) => {
      state.categories.isLoading = true;
      state.categories.error = null;
    },
    setCategoriesSuccess: (state, action: PayloadAction<{ categories: Category[]; total: number }>) => {
      state.categories.items = action.payload.categories;
      state.categories.total = action.payload.total;
      state.categories.isLoading = false;
      state.categories.error = null;
    },
    setCategoriesError: (state, action: PayloadAction<string>) => {
      state.categories.isLoading = false;
      state.categories.error = action.payload;
    },
    setSelectedCategories: (state, action: PayloadAction<string[]>) => {
      state.selectedCategories = action.payload;
    },
    setShowArchived: (state, action: PayloadAction<boolean>) => {
      state.showArchived = action.payload;
    },
    clearFilters: (state) => {
      state.selectedCategories = [];
      state.showArchived = false;
    },
    setSortingState: (state, action: PayloadAction<{ column: string; direction: 'asc' | 'desc' }>) => {
      state.sortColumn = action.payload.column;
      state.sortDirection = action.payload.direction;
    },
  },
});

export const {
  setCategoriesLoading,
  setCategoriesSuccess,
  setCategoriesError,
  setSelectedCategories,
  setShowArchived,
  clearFilters,
  setSortingState
} = filtersSlice.actions;

export default filtersSlice.reducer; 