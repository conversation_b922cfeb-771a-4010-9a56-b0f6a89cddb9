import { configureStore } from '@reduxjs/toolkit';
import memoriesReducer from './memoriesSlice';
import uiReducer from './uiSlice';
import filtersReducer from './filtersSlice';
import configReducer from './configSlice';
import userReducer from './userSlice';

export const store = configureStore({
  reducer: {
    memories: memoriesReducer,
    ui: uiReducer,
    filters: filtersReducer,
    config: configReducer,
    user: userReducer,
  },
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {memories: MemoriesState, ui: UIState, filters: FiltersState, config: ConfigState, user: UserState}
export type AppDispatch = typeof store.dispatch; 