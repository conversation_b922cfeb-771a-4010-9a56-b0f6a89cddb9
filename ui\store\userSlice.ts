import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface User {
  user_id: string;
  email: string;
  name?: string;
  email_verified?: boolean;
  is_authenticated?: boolean;
  supabase_user_id?: string;
  created_at?: string;
  last_sign_in_at?: string;
}

interface UserState {
  availableUsers: User[];
  selectedUser: User | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: UserState = {
  availableUsers: [],
  selectedUser: null,
  isLoading: false,
  error: null,
};

// Async thunk to fetch users
export const fetchUsers = createAsyncThunk(
  'user/fetchUsers',
  async (_, { rejectWithValue }) => {
    try {
      const URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8765";
      const response = await fetch(`${URL}/api/v1/auth/users`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.users;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch users');
    }
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setSelectedUser: (state, action: PayloadAction<User | null>) => {
      state.selectedUser = action.payload;
      // Persist to localStorage
      if (action.payload) {
        localStorage.setItem('selectedUser', JSON.stringify(action.payload));
      } else {
        localStorage.removeItem('selectedUser');
      }
    },
    loadSelectedUserFromStorage: (state) => {
      try {
        const storedUser = localStorage.getItem('selectedUser');
        if (storedUser) {
          state.selectedUser = JSON.parse(storedUser);
        }
      } catch (error) {
        console.error('Error loading selected user from storage:', error);
        localStorage.removeItem('selectedUser');
      }
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUsers.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.availableUsers = action.payload;
        state.error = null;
        
        // If no user is selected, select the first one
        if (!state.selectedUser && action.payload.length > 0) {
          state.selectedUser = action.payload[0];
          localStorage.setItem('selectedUser', JSON.stringify(action.payload[0]));
        }
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setSelectedUser, loadSelectedUserFromStorage, clearError } = userSlice.actions;
export default userSlice.reducer;